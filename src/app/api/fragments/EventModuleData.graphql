fragment EventModuleData on Module {
    id
    displayName

    company {
        ...CompanyInModuleOptionData
        timeZone
    }

    ... on EventApplicationModule {
        showResetKYCButton

        customerModule {
            ... on LocalCustomerManagementModule {
                kycFields {
                    ...LocalCustomerManagementModuleKycFieldSpecs
                }
                extraSettings {
                    mobileVerification
                }
            }
        }

        agreementsModule {
            id
        }
        showRemoteFlowButtonInKYCPage

        appointmentModule {
            ...AppointmentModuleOnEventModuleData
        }

        visitAppointmentModule {
            id
            displayName

            ... on VisitAppointmentModule {
                unavailableDayOfWeek
                bookingTimeSlot {
                    ...TimeSlotData
                }

                advancedBookingLimit
                maxAdvancedBookingLimit
                bookingInformation {
                    ...TranslatedStringData
                }
            }

            company {
                ...CompanyInModuleOptionData
                timeZone
                countryCode
            }
        }

        displayAppointmentDatepicker
        displayVisitAppointmentDatepicker

        vehicleModuleId

        dealerVehicles {
            dealerId
            vehicleSuiteIds
        }

        liveChatSettingId

        liveChatSetting {
            id

            ... on WhatsappLiveChatSetting {
                link
            }

            ... on UserlikeChatbotSetting {
                script
            }
        }

        capModule {
            id
            enableCampaignIdMapping
        }

        emailContents {
            ...EventApplicationModuleEmailContentSpecs
        }
    }
}
