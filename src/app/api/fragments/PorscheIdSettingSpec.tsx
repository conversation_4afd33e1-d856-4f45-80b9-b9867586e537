import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
export type PorscheIdSettingSpecFragment = (
  { __typename: 'PorscheIdSetting' }
  & Pick<SchemaTypes.PorscheIdSetting, 'apiKey' | 'identityProvider' | 'audience' | 'userDataBaseUrl'>
);

export const PorscheIdSettingSpecFragmentDoc = /*#__PURE__*/ gql`
    fragment PorscheIdSettingSpec on PorscheIdSetting {
  apiKey
  identityProvider
  audience
  userDataBaseUrl
}
    `;