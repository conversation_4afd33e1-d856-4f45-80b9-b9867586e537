import type * as SchemaTypes from '../types';

import type { MyInfoSettingsListFragment } from './MyInfoSettingsList';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import { gql } from '@apollo/client';
import { MyInfoSettingsListFragmentDoc } from './MyInfoSettingsList';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
export type MyInfoModuleListItemSpecsFragment = (
  { __typename: 'MyInfoModule' }
  & Pick<SchemaTypes.MyInfoModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName' | 'timeZone'>
  ), myInfoSettings: Array<(
    { __typename: 'MyInfoSetting' }
    & MyInfoSettingsListFragment
  )>, versioning: (
    { __typename: 'SimpleVersioning' }
    & SimpleVersioningDataFragment
  ) }
);

export const MyInfoModuleListItemSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment MyInfoModuleListItemSpecs on MyInfoModule {
  id
  displayName
  company {
    id
    displayName
    timeZone
  }
  myInfoSettings {
    ...MyInfoSettingsList
  }
  versioning {
    ...SimpleVersioningData
  }
}
    `;