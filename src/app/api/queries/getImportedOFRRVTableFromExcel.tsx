import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetImportedOfrrvTableFromExcelQueryVariables = SchemaTypes.Exact<{
  upload: SchemaTypes.Scalars['Upload']['input'];
}>;


export type GetImportedOfrrvTableFromExcelQuery = (
  { __typename: 'Query' }
  & { getImportedOFRRVTableFromExcel: (
    { __typename: 'OfrImportedRVTable' }
    & Pick<SchemaTypes.OfrImportedRvTable, 'errors'>
    & { table: Array<(
      { __typename: 'OfrRVTableCell' }
      & Pick<SchemaTypes.OfrRvTableCell, 'year' | 'value' | 'model'>
    )> }
  ) }
);


export const GetImportedOfrrvTableFromExcelDocument = /*#__PURE__*/ gql`
    query getImportedOFRRVTableFromExcel($upload: Upload!) {
  getImportedOFRRVTableFromExcel(upload: $upload) {
    table {
      year
      value
      model
    }
    errors
  }
}
    `;

/**
 * __useGetImportedOfrrvTableFromExcelQuery__
 *
 * To run a query within a React component, call `useGetImportedOfrrvTableFromExcelQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetImportedOfrrvTableFromExcelQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetImportedOfrrvTableFromExcelQuery({
 *   variables: {
 *      upload: // value for 'upload'
 *   },
 * });
 */
export function useGetImportedOfrrvTableFromExcelQuery(baseOptions: Apollo.QueryHookOptions<GetImportedOfrrvTableFromExcelQuery, GetImportedOfrrvTableFromExcelQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetImportedOfrrvTableFromExcelQuery, GetImportedOfrrvTableFromExcelQueryVariables>(GetImportedOfrrvTableFromExcelDocument, options);
      }
export function useGetImportedOfrrvTableFromExcelLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetImportedOfrrvTableFromExcelQuery, GetImportedOfrrvTableFromExcelQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetImportedOfrrvTableFromExcelQuery, GetImportedOfrrvTableFromExcelQueryVariables>(GetImportedOfrrvTableFromExcelDocument, options);
        }
export type GetImportedOfrrvTableFromExcelQueryHookResult = ReturnType<typeof useGetImportedOfrrvTableFromExcelQuery>;
export type GetImportedOfrrvTableFromExcelLazyQueryHookResult = ReturnType<typeof useGetImportedOfrrvTableFromExcelLazyQuery>;
export type GetImportedOfrrvTableFromExcelQueryResult = Apollo.QueryResult<GetImportedOfrrvTableFromExcelQuery, GetImportedOfrrvTableFromExcelQueryVariables>;