import type * as SchemaTypes from '../types';

import type { LabelsDataFragment } from '../fragments/LabelsData';
import type {
    ModuleSpecs_AdyenPaymentModule_Fragment,
    ModuleSpecs_AppointmentModule_Fragment,
    ModuleSpecs_AutoplayModule_Fragment,
    ModuleSpecs_BankModule_Fragment,
    ModuleSpecs_BasicSigningModule_Fragment,
    ModuleSpecs_CapModule_Fragment,
    ModuleSpecs_ConfiguratorModule_Fragment,
    ModuleSpecs_ConsentsAndDeclarationsModule_Fragment,
    ModuleSpecs_CtsModule_Fragment,
    ModuleSpecs_DocusignModule_Fragment,
    ModuleSpecs_EventApplicationModule_Fragment,
    ModuleSpecs_FinderApplicationPrivateModule_Fragment,
    ModuleSpecs_FinderApplicationPublicModule_Fragment,
    ModuleSpecs_FinderVehicleManagementModule_Fragment,
    ModuleSpecs_FiservPaymentModule_Fragment,
    ModuleSpecs_GiftVoucherModule_Fragment,
    ModuleSpecs_InsuranceModule_Fragment,
    ModuleSpecs_LabelsModule_Fragment,
    ModuleSpecs_LaunchPadModule_Fragment,
    ModuleSpecs_LocalCustomerManagementModule_Fragment,
    ModuleSpecs_MaintenanceModule_Fragment,
    ModuleSpecs_MarketingModule_Fragment,
    ModuleSpecs_MobilityModule_Fragment,
    ModuleSpecs_MyInfoModule_Fragment,
    ModuleSpecs_NamirialSigningModule_Fragment,
    ModuleSpecs_OfrModule_Fragment,
    ModuleSpecs_OidcModule_Fragment,
    ModuleSpecs_PayGatePaymentModule_Fragment,
    ModuleSpecs_PorscheIdModule_Fragment,
    ModuleSpecs_PorscheMasterDataModule_Fragment,
    ModuleSpecs_PorschePaymentModule_Fragment,
    ModuleSpecs_PorscheRetainModule_Fragment,
    ModuleSpecs_PromoCodeModule_Fragment,
    ModuleSpecs_SalesControlBoardModule_Fragment,
    ModuleSpecs_SalesOfferModule_Fragment,
    ModuleSpecs_SimpleVehicleManagementModule_Fragment,
    ModuleSpecs_StandardApplicationModule_Fragment,
    ModuleSpecs_TradeInModule_Fragment,
    ModuleSpecs_TtbPaymentModule_Fragment,
    ModuleSpecs_UserlikeChatbotModule_Fragment,
    ModuleSpecs_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
    ModuleSpecs_VisitAppointmentModule_Fragment,
    ModuleSpecs_WebsiteModule_Fragment,
    ModuleSpecs_WhatsappLiveChatModule_Fragment,
} from '../fragments/ModuleSpecs';
import type { ConsentsAndDeclarationsModuleSpecsFragment } from '../fragments/ConsentsAndDeclarationsModuleSpecs';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type {
    AuthorData_CorporateCustomer_Fragment,
    AuthorData_ExternalBank_Fragment,
    AuthorData_Guarantor_Fragment,
    AuthorData_LocalCustomer_Fragment,
    AuthorData_PorscheRetain_Fragment,
    AuthorData_Salesforce_Fragment,
    AuthorData_SystemBank_Fragment,
    AuthorData_User_Fragment,
} from '../fragments/AuthorData';
import type { SimpleVehicleManagementModuleSpecsFragment } from '../fragments/SimpleVehicleManagementModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from '../fragments/CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { LocalCustomerManagementModuleSpecsFragment } from '../fragments/LocalCustomerManagementModuleSpecs';
import type { LocalCustomerManagementModuleKycFieldSpecsFragment } from '../fragments/LocalCustomerManagementModuleKycFieldSpecs';
import type { KycExtraSettingsSpecsFragment } from '../fragments/KYCExtraSettingsSpecs';
import type { KycPresetsSpecFragment } from '../fragments/KYCPresetsSpec';
import type {
    ConditionSpecs_ApplicationModuleCondition_Fragment,
    ConditionSpecs_BankCondition_Fragment,
    ConditionSpecs_ContextualCondition_Fragment,
    ConditionSpecs_DealerCondition_Fragment,
    ConditionSpecs_GiftVoucherCondition_Fragment,
    ConditionSpecs_InsurerCondition_Fragment,
    ConditionSpecs_LocationCondition_Fragment,
    ConditionSpecs_LogicCondition_Fragment,
    ConditionSpecs_SalesOfferAgreementsCondition_Fragment,
} from '../fragments/ConditionSpecs';
import type {
    BaseConditionSpecs_ApplicationModuleCondition_Fragment,
    BaseConditionSpecs_BankCondition_Fragment,
    BaseConditionSpecs_ContextualCondition_Fragment,
    BaseConditionSpecs_DealerCondition_Fragment,
    BaseConditionSpecs_GiftVoucherCondition_Fragment,
    BaseConditionSpecs_InsurerCondition_Fragment,
    BaseConditionSpecs_LocationCondition_Fragment,
    BaseConditionSpecs_LogicCondition_Fragment,
    BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment,
} from '../fragments/BaseConditionSpecs';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import type { BankModuleSpecsFragment } from '../fragments/BankModuleSpecs';
import type { BasicSigningModuleSpecsFragment } from '../fragments/BasicSigningModuleSpecs';
import type { NamirialSigningModuleSpecsFragment } from '../fragments/NamirialSigningModuleSpecs';
import type { NamirialSettingsSpecFragment } from '../fragments/NamirialSettingsSpec';
import type { StandardApplicationModuleSpecsFragment } from '../fragments/StandardApplicationModuleSpecs';
import type { DealerPriceDisclaimerDataFragment } from '../fragments/DealerPriceDisclaimerData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { DepositAmountDataFragment } from '../fragments/DepositAmountData';
import type {
    ApplicationMarketTypeFragment_DefaultApplicationMarket_Fragment,
    ApplicationMarketTypeFragment_NewZealandApplicationMarket_Fragment,
    ApplicationMarketTypeFragment_SingaporeApplicationMarket_Fragment,
} from '../fragments/ApplicationMarketTypeFragment';
import type { DealerMarketDataFragment } from '../fragments/DealerMarketData';
import type { BankDealerMarketDataFragment } from '../fragments/BankDealerMarketData';
import type { NzFeesDealerMarketDataFragment } from '../fragments/NzFeesDealerMarketData';
import type { DealerVehiclesSpecsFragment } from '../fragments/DealerVehiclesSpecs';
import type { DealerFinanceProductsSpecsFragment } from '../fragments/DealerFinanceProductsSpecs';
import type {
    FinanceProductListData_LocalDeferredPrincipal_Fragment,
    FinanceProductListData_LocalHirePurchase_Fragment,
    FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment,
    FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment,
    FinanceProductListData_LocalLease_Fragment,
    FinanceProductListData_LocalLeasePurchase_Fragment,
    FinanceProductListData_LocalUcclLeasing_Fragment,
} from '../fragments/FinanceProductListData';
import type { PeriodDataFragment } from '../fragments/PeriodData';
import type {
    ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment,
    ModulesCompanyTimezoneData_AppointmentModule_Fragment,
    ModulesCompanyTimezoneData_AutoplayModule_Fragment,
    ModulesCompanyTimezoneData_BankModule_Fragment,
    ModulesCompanyTimezoneData_BasicSigningModule_Fragment,
    ModulesCompanyTimezoneData_CapModule_Fragment,
    ModulesCompanyTimezoneData_ConfiguratorModule_Fragment,
    ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment,
    ModulesCompanyTimezoneData_CtsModule_Fragment,
    ModulesCompanyTimezoneData_DocusignModule_Fragment,
    ModulesCompanyTimezoneData_EventApplicationModule_Fragment,
    ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment,
    ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment,
    ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment,
    ModulesCompanyTimezoneData_FiservPaymentModule_Fragment,
    ModulesCompanyTimezoneData_GiftVoucherModule_Fragment,
    ModulesCompanyTimezoneData_InsuranceModule_Fragment,
    ModulesCompanyTimezoneData_LabelsModule_Fragment,
    ModulesCompanyTimezoneData_LaunchPadModule_Fragment,
    ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment,
    ModulesCompanyTimezoneData_MaintenanceModule_Fragment,
    ModulesCompanyTimezoneData_MarketingModule_Fragment,
    ModulesCompanyTimezoneData_MobilityModule_Fragment,
    ModulesCompanyTimezoneData_MyInfoModule_Fragment,
    ModulesCompanyTimezoneData_NamirialSigningModule_Fragment,
    ModulesCompanyTimezoneData_OfrModule_Fragment,
    ModulesCompanyTimezoneData_OidcModule_Fragment,
    ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment,
    ModulesCompanyTimezoneData_PorscheIdModule_Fragment,
    ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment,
    ModulesCompanyTimezoneData_PorschePaymentModule_Fragment,
    ModulesCompanyTimezoneData_PorscheRetainModule_Fragment,
    ModulesCompanyTimezoneData_PromoCodeModule_Fragment,
    ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment,
    ModulesCompanyTimezoneData_SalesOfferModule_Fragment,
    ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment,
    ModulesCompanyTimezoneData_StandardApplicationModule_Fragment,
    ModulesCompanyTimezoneData_TradeInModule_Fragment,
    ModulesCompanyTimezoneData_TtbPaymentModule_Fragment,
    ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment,
    ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment,
    ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment,
    ModulesCompanyTimezoneData_WebsiteModule_Fragment,
    ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment,
} from '../fragments/ModulesCompanyTimezoneData';
import type { VehicleReferenceParametersDataFragment } from '../fragments/VehicleReferenceParametersData';
import type {
    BalloonSettingsDetails_BalloonRangeSettings_Fragment,
    BalloonSettingsDetails_BalloonTableSettings_Fragment,
} from '../fragments/BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from '../fragments/BalloonGFVSettingsDetails';
import type { DealerInsuranceProductsSpecsFragment } from '../fragments/DealerInsuranceProductsSpecs';
import type {
    InsuranceProductListData_Eazy_Fragment,
    InsuranceProductListData_ErgoLookupTable_Fragment,
} from '../fragments/InsuranceProductListData';
import type { ErgoLookupTableSettingsDetailsFragment } from '../fragments/ErgoLookupTableSettingDetails';
import type { LocalModelSpecsFragment } from '../fragments/LocalModelSpecs';
import type { LocalMakeSpecsFragment } from '../fragments/LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { DealerDisclaimersConfiguratorDataFragment } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import type { KycPresetsOptionsDataFragment } from '../fragments/KYCPresetsOptionsData';
import type {
    DealershipSettingSpecData_DealershipMyInfoSetting_Fragment,
    DealershipSettingSpecData_DealershipPaymentSetting_Fragment,
    DealershipSettingSpecData_DealershipPublicSalesPerson_Fragment,
} from '../fragments/DealershipSettingSpecData';
import type { FlexibleDiscountDataFragment } from '../fragments/FlexibleDiscountData';
import type { CounterSettingsSpecsFragment } from '../fragments/CounterSettingsSpecs';
import type {
    StandardApplicationModuleEmailContentsSpecsFragment,
    StandardApplicationModuleEmailContentCustomerSpecsFragment,
    StandardApplicationModuleEmailContentShareSubmissionSpecsFragment,
    StandardApplicationModuleEmailContentSpecsFragment,
    StandardApplicationModuleEmailContentSalesPersonSpecsFragment,
} from '../fragments/StandardApplicationModuleEmailContentsSpecs';
import type { DealerTranslatedStringSettingDataFragment } from '../fragments/DealerTranslatedStringData';
import type { DealerUploadedFileWithPreviewDataFragment } from '../fragments/DealerUploadedFileWithPreview';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { DealerBooleanSettingDataFragment } from '../fragments/DealerBooleanSettingData';
import type { EventApplicationModuleSpecsFragment } from '../fragments/EventApplicationModuleSpecs';
import type { AppointmentModuleOnEventModuleDataFragment } from '../fragments/AppointmentModuleOnEventModuleData';
import type { AppointmentTimeSlotDataFragment } from '../fragments/AppointmentTimeSlotData';
import type {
    EventApplicationModuleEmailContentSpecsFragment,
    EventEmailContentSpecsFragment,
} from '../fragments/EventApplicationModuleEmailContentSpecs';
import type { TranslatedTextDataFragment } from '../fragments/TranslationTextData';
import type { AdyenPaymentModuleSpecsFragment } from '../fragments/AdyenPaymentModuleSpecs';
import type { AdyenPaymentSettingsSpecFragment } from '../fragments/AdyenPaymentSettingsSpec';
import type { PorschePaymentModuleSpecsFragment } from '../fragments/PorschePaymentModuleSpecs';
import type { PorschePaymentSettingsSpecFragment } from '../fragments/PorschePaymentSettingsSpec';
import type { FiservPaymentModuleSpecsFragment } from '../fragments/FiservPaymentModuleSpecs';
import type { FiservPaymentSettingsSpecFragment } from '../fragments/FiservPaymentSettingsSpec';
import type { PayGatePaymentModuleSpecsFragment } from '../fragments/PayGatePaymentModuleSpecs';
import type { PayGatePaymentSettingsSpecFragment } from '../fragments/PayGatePaymentSettingsSpec';
import type { TtbPaymentModuleSpecsFragment } from '../fragments/TtbPaymentModuleSpecs';
import type { TtbPaymentSettingsSpecFragment } from '../fragments/TtbPaymentSettingsSpec';
import type { MyInfoModuleSpecsFragment } from '../fragments/MyInfoModuleSpecs';
import type { MyInfoSettingSpecFragment } from '../fragments/MyInfoSettingSpec';
import type { ConfiguratorModuleSpecsFragment } from '../fragments/ConfiguratorModuleSpecs';
import type { ConfiguratorModuleEmailContentSpecsFragment } from '../fragments/ConfiguratorModuleEmailContentSpecs';
import type { WhatsappLiveChatModuleSpecsFragment } from '../fragments/WhatsappLiveChatModuleSpecs';
import type { WhatsappLiveChatSettingsSpecFragment } from '../fragments/WhatsappLiveChatSettingsSpec';
import type { UserlikeChatbotModuleSpecsFragment } from '../fragments/UserlikeChatbotModuleSpecs';
import type { UserlikeChatbotSettingsSpecFragment } from '../fragments/UserlikeChatbotSettingsSpec';
import type { PromoCodeModuleSpecsFragment } from '../fragments/PromoCodeModuleSpecs';
import type { MaintenanceModuleSpecsFragment } from '../fragments/MaintenanceModuleSpecs';
import type { WebsiteModuleSpecsFragment } from '../fragments/WebsiteModuleSpecs';
import type { EdmSocialMediaDataFragment } from '../fragments/EdmSocialMediaData';
import type { MobilityModuleSpecsFragment } from '../fragments/MobilityModuleSpecs';
import type { DealerBookingCodeSpecsFragment } from '../fragments/DealerBookingCodeSpecs';
import type { MobilitySigningSettingSpecsFragment } from '../fragments/MobilitySigningSettingSpecs';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from '../fragments/MobilityModuleEmailScenarioContentSpecs';
import type { MobilityCustomerEmailContentDataFragment } from '../fragments/MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from '../fragments/MobilityEmailContentData';
import type { MobilityOperatorEmailContentDataFragment } from '../fragments/MobilityOperatorEmailContentData';
import type { MobilityHomeDeliveryDataFragment } from '../fragments/MobilityHomeDeliveryData';
import type { LabelsModuleSpecsFragment } from '../fragments/LabelsModuleSpecs';
import type { FinderVehicleManagementModuleSpecsFragment } from '../fragments/FinderVehicleManagementModuleSpecs';
import type { FinderApplicationPublicModuleSpecsFragment } from '../fragments/FinderApplicationPublicModuleSpecs';
import type { FinderApplicationModuleEmailContentSpecsFragment } from '../fragments/FinderApplicationModuleEmailContentSpecs';
import type { ModuleDisclaimersDataFragment } from '../fragments/ModuleDisclaimersData';
import type { FinderApplicationPrivateModuleSpecsFragment } from '../fragments/FinderApplicationPrivateModuleSpecs';
import type { AutoplayModuleSpecsFragment } from '../fragments/AutoplayModuleSpecs';
import type { AutoplaySettingSpecsFragment } from '../fragments/AutoplaySettingSpecs';
import type { CtsModuleSpecsFragment } from '../fragments/CtsModuleSpecs';
import type { CtsModuleSettingDataFragment } from '../fragments/CtsModuleSettingData';
import type { AppointmentModuleSpecsFragment } from '../fragments/AppointmentModuleSpecs';
import type {
    AppointmentModuleEmailContentsSpecsFragment,
    AppointmentModuleEmailContentCustomerSpecsFragment,
    AppointmentModuleEmailContentSpecsFragment,
    AppointmentModuleEmailContentSalesPersonSpecsFragment,
    AppointmentModuleEmailContentFinderReservationSpecsFragment,
} from '../fragments/AppointmentModuleEmailContentsSpecs';
import type { InsuranceModuleSpecsFragment } from '../fragments/InsuranceModuleSpecs';
import type { PorscheMasterDataModuleSpecsFragment } from '../fragments/PorscheMasterDataModuleSpecs';
import type { GiftVoucherModuleSpecsFragment } from '../fragments/GiftVoucherModuleSpecs';
import type {
    GiftVoucherModuleEmailContentsSpecsFragment,
    GiftVoucherModuleEmailContentCustomerSpecsFragment,
    GiftVoucherModuleEmailDataFragment,
} from '../fragments/GiftVoucherModuleEmailContentsSpecs';
import type { TradeInModuleSpecsFragment } from '../fragments/TradeInModuleSpecs';
import type { TradeInSettingSpecFragment } from '../fragments/TradeInSetting';
import type { CapModuleSpecsFragment } from '../fragments/CapModuleSpecs';
import type { CapSettingSpecFragment } from '../fragments/CapSettingSpec';
import type { PorscheIdModuleSpecsFragment } from '../fragments/PorscheIdModuleSpecs';
import type { PorscheIdSettingSpecFragment } from '../fragments/PorscheIdSettingSpec';
import type { PorscheRetainModuleSpecsFragment } from '../fragments/PorscheRetainModuleSpecs';
import type { DocusignModuleSpecsFragment } from '../fragments/DocusignModuleSpecs';
import type { DocusignSettingDataFragment } from '../fragments/DocusignSettingData';
import type { LaunchPadModuleSpecsFragment } from '../fragments/LaunchPadModuleSpecs';
import type { VisitAppointmentModuleSpecsFragment } from '../fragments/VisitAppointmentModuleSpecs';
import type { TimeSlotDataFragment } from '../fragments/TimeSlotData';
import type {
    VisitAppointmentModuleEmailContentsSpecsFragment,
    VisitAppointmentModuleEmailContentCustomerSpecsFragment,
    VisitAppointmentModuleEmailContentSpecsFragment,
    VisitAppointmentModuleEmailContentSalesPersonSpecsFragment,
} from '../fragments/VisitAppointmentModuleEmailContentsSpecs';
import type { OidcModuleSpecsFragment } from '../fragments/OIDCModuleSpecs';
import type { MarketingModuleSpecsFragment } from '../fragments/MarketingModuleSpecs';
import type { SalesOfferModuleSpecsFragment } from '../fragments/SalesOfferModuleSpecs';
import type { BankDetailsDataFragment } from '../fragments/BankDetailsData';
import type { TranslatedStringSpecsFragment } from '../fragments/TranslatedStringSpecs';
import type {
    BankIntegrationData_DbsBankIntegration_Fragment,
    BankIntegrationData_EmailBankIntegration_Fragment,
    BankIntegrationData_EnbdBankIntegration_Fragment,
    BankIntegrationData_HlfBankIntegration_Fragment,
    BankIntegrationData_HlfBankV2Integration_Fragment,
    BankIntegrationData_MaybankIntegration_Fragment,
    BankIntegrationData_UobBankIntegration_Fragment,
} from '../fragments/BankIntegrationData';
import type { UploadFileFormDataFragment } from '../fragments/UploadFileFormData';
import type {
    FinanceProductDetailsData_LocalDeferredPrincipal_Fragment,
    FinanceProductDetailsData_LocalHirePurchase_Fragment,
    FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment,
    FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment,
    FinanceProductDetailsData_LocalLease_Fragment,
    FinanceProductDetailsData_LocalLeasePurchase_Fragment,
    FinanceProductDetailsData_LocalUcclLeasing_Fragment,
} from '../fragments/FinanceProductDetailsData';
import type { PaymentSettingsDetailsFragment } from '../fragments/PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from '../fragments/LoanSettingsDetails';
import type {
    TermSettingsDetails_DeferredPrincipalTermSettings_Fragment,
    TermSettingsDetails_GenericPrincipalTermSettings_Fragment,
} from '../fragments/TermSettingsDetails';
import type {
    InterestRateSettingsDetails_InterestRateFixedSettings_Fragment,
    InterestRateSettingsDetails_InterestRateRangeSettings_Fragment,
    InterestRateSettingsDetails_InterestRateTableSettings_Fragment,
} from '../fragments/InterestRateSettingsDetails';
import type {
    DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment,
    DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment,
} from '../fragments/DownPaymentSettingsDetails';
import type { LeaseSettingsDetailsFragment } from '../fragments/LeaseSettingsDetails';
import type {
    DepositSettingsDetails_DepositRangeSettings_Fragment,
    DepositSettingsDetails_DepositTableSettings_Fragment,
} from '../fragments/DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from '../fragments/ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from '../fragments/LocalUcclLeasingOnlyDetails';
import type {
    SalesOfferModuleEmailContentsSpecsFragment,
    SalesOfferEmailContentsSpecsFragment,
} from '../fragments/SalesOfferModuleEmailContentsSpecs';
import type { VehicleDataWithPorscheCodeIntegrationModuleSpecsFragment } from '../fragments/VehicleDataWithPorscheCodeIntegrationModuleSpecs';
import type { SalesControlBoardModuleSpecsFragment } from '../fragments/SalesControlBoardModuleSpecs';
import type {
    DealerIntDataFragment,
    DealerFloatDataFragment,
    DealerObjectIdDataFragment,
} from '../fragments/DealerIntData';
import type { OfrModuleSpecsFragment } from '../fragments/OFRModuleSpecs';
import type {
    OfrModuleEmailContentsSpecsFragment,
    OfrSalesConsultantEmailContentSpecsFragment,
    OfrSalesConsultantEmailContentContextSpecsFragment,
    OfrCustomerEmailContentSpecsFragment,
    OfrCustomerEmailContentContextSpecsFragment,
} from '../fragments/OFRModuleEmailContentsSpecs';
import type { OfrEquityDataFragment } from '../fragments/OFREquityData';
import type { ModuleVariantDataFragment } from '../fragments/ModuleVariantData';
import type { LocalVariantSpecsFragment } from '../fragments/LocalVariantSpecs';
import { gql } from '@apollo/client';
import { LabelsDataFragmentDoc } from '../fragments/LabelsData';
import { ModuleSpecsFragmentDoc } from '../fragments/ModuleSpecs';
import { ConsentsAndDeclarationsModuleSpecsFragmentDoc } from '../fragments/ConsentsAndDeclarationsModuleSpecs';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { SimpleVehicleManagementModuleSpecsFragmentDoc } from '../fragments/SimpleVehicleManagementModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from '../fragments/CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { LocalCustomerManagementModuleSpecsFragmentDoc } from '../fragments/LocalCustomerManagementModuleSpecs';
import { LocalCustomerManagementModuleKycFieldSpecsFragmentDoc } from '../fragments/LocalCustomerManagementModuleKycFieldSpecs';
import { KycExtraSettingsSpecsFragmentDoc } from '../fragments/KYCExtraSettingsSpecs';
import { KycPresetsSpecFragmentDoc } from '../fragments/KYCPresetsSpec';
import { ConditionSpecsFragmentDoc } from '../fragments/ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from '../fragments/BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import { BankModuleSpecsFragmentDoc } from '../fragments/BankModuleSpecs';
import { BasicSigningModuleSpecsFragmentDoc } from '../fragments/BasicSigningModuleSpecs';
import { NamirialSigningModuleSpecsFragmentDoc } from '../fragments/NamirialSigningModuleSpecs';
import { NamirialSettingsSpecFragmentDoc } from '../fragments/NamirialSettingsSpec';
import { StandardApplicationModuleSpecsFragmentDoc } from '../fragments/StandardApplicationModuleSpecs';
import { DealerPriceDisclaimerDataFragmentDoc } from '../fragments/DealerPriceDisclaimerData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { DepositAmountDataFragmentDoc } from '../fragments/DepositAmountData';
import { ApplicationMarketTypeFragmentFragmentDoc } from '../fragments/ApplicationMarketTypeFragment';
import { DealerMarketDataFragmentDoc } from '../fragments/DealerMarketData';
import { BankDealerMarketDataFragmentDoc } from '../fragments/BankDealerMarketData';
import { NzFeesDealerMarketDataFragmentDoc } from '../fragments/NzFeesDealerMarketData';
import { DealerVehiclesSpecsFragmentDoc } from '../fragments/DealerVehiclesSpecs';
import { DealerFinanceProductsSpecsFragmentDoc } from '../fragments/DealerFinanceProductsSpecs';
import { FinanceProductListDataFragmentDoc } from '../fragments/FinanceProductListData';
import { PeriodDataFragmentDoc } from '../fragments/PeriodData';
import { ModulesCompanyTimezoneDataFragmentDoc } from '../fragments/ModulesCompanyTimezoneData';
import { VehicleReferenceParametersDataFragmentDoc } from '../fragments/VehicleReferenceParametersData';
import { BalloonSettingsDetailsFragmentDoc } from '../fragments/BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from '../fragments/BalloonGFVSettingsDetails';
import { DealerInsuranceProductsSpecsFragmentDoc } from '../fragments/DealerInsuranceProductsSpecs';
import { InsuranceProductListDataFragmentDoc } from '../fragments/InsuranceProductListData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from '../fragments/ErgoLookupTableSettingDetails';
import { LocalModelSpecsFragmentDoc } from '../fragments/LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from '../fragments/LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import { KycPresetsOptionsDataFragmentDoc } from '../fragments/KYCPresetsOptionsData';
import { DealershipSettingSpecDataFragmentDoc } from '../fragments/DealershipSettingSpecData';
import { FlexibleDiscountDataFragmentDoc } from '../fragments/FlexibleDiscountData';
import { CounterSettingsSpecsFragmentDoc } from '../fragments/CounterSettingsSpecs';
import {
    StandardApplicationModuleEmailContentsSpecsFragmentDoc,
    StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc,
    StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc,
    StandardApplicationModuleEmailContentSpecsFragmentDoc,
    StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc,
} from '../fragments/StandardApplicationModuleEmailContentsSpecs';
import { DealerTranslatedStringSettingDataFragmentDoc } from '../fragments/DealerTranslatedStringData';
import { DealerUploadedFileWithPreviewDataFragmentDoc } from '../fragments/DealerUploadedFileWithPreview';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { DealerBooleanSettingDataFragmentDoc } from '../fragments/DealerBooleanSettingData';
import { EventApplicationModuleSpecsFragmentDoc } from '../fragments/EventApplicationModuleSpecs';
import { AppointmentModuleOnEventModuleDataFragmentDoc } from '../fragments/AppointmentModuleOnEventModuleData';
import { AppointmentTimeSlotDataFragmentDoc } from '../fragments/AppointmentTimeSlotData';
import { EventApplicationModuleEmailContentSpecsFragmentDoc } from '../fragments/EventApplicationModuleEmailContentSpecs';
import { TranslatedTextDataFragmentDoc } from '../fragments/TranslationTextData';
import { AdyenPaymentModuleSpecsFragmentDoc } from '../fragments/AdyenPaymentModuleSpecs';
import { AdyenPaymentSettingsSpecFragmentDoc } from '../fragments/AdyenPaymentSettingsSpec';
import { PorschePaymentModuleSpecsFragmentDoc } from '../fragments/PorschePaymentModuleSpecs';
import { PorschePaymentSettingsSpecFragmentDoc } from '../fragments/PorschePaymentSettingsSpec';
import { FiservPaymentModuleSpecsFragmentDoc } from '../fragments/FiservPaymentModuleSpecs';
import { FiservPaymentSettingsSpecFragmentDoc } from '../fragments/FiservPaymentSettingsSpec';
import { PayGatePaymentModuleSpecsFragmentDoc } from '../fragments/PayGatePaymentModuleSpecs';
import { PayGatePaymentSettingsSpecFragmentDoc } from '../fragments/PayGatePaymentSettingsSpec';
import { TtbPaymentModuleSpecsFragmentDoc } from '../fragments/TtbPaymentModuleSpecs';
import { TtbPaymentSettingsSpecFragmentDoc } from '../fragments/TtbPaymentSettingsSpec';
import { MyInfoModuleSpecsFragmentDoc } from '../fragments/MyInfoModuleSpecs';
import { MyInfoSettingSpecFragmentDoc } from '../fragments/MyInfoSettingSpec';
import { ConfiguratorModuleSpecsFragmentDoc } from '../fragments/ConfiguratorModuleSpecs';
import { ConfiguratorModuleEmailContentSpecsFragmentDoc } from '../fragments/ConfiguratorModuleEmailContentSpecs';
import { WhatsappLiveChatModuleSpecsFragmentDoc } from '../fragments/WhatsappLiveChatModuleSpecs';
import { WhatsappLiveChatSettingsSpecFragmentDoc } from '../fragments/WhatsappLiveChatSettingsSpec';
import { UserlikeChatbotModuleSpecsFragmentDoc } from '../fragments/UserlikeChatbotModuleSpecs';
import { UserlikeChatbotSettingsSpecFragmentDoc } from '../fragments/UserlikeChatbotSettingsSpec';
import { PromoCodeModuleSpecsFragmentDoc } from '../fragments/PromoCodeModuleSpecs';
import { MaintenanceModuleSpecsFragmentDoc } from '../fragments/MaintenanceModuleSpecs';
import { WebsiteModuleSpecsFragmentDoc } from '../fragments/WebsiteModuleSpecs';
import { EdmSocialMediaDataFragmentDoc } from '../fragments/EdmSocialMediaData';
import { MobilityModuleSpecsFragmentDoc } from '../fragments/MobilityModuleSpecs';
import { DealerBookingCodeSpecsFragmentDoc } from '../fragments/DealerBookingCodeSpecs';
import { MobilitySigningSettingSpecsFragmentDoc } from '../fragments/MobilitySigningSettingSpecs';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from '../fragments/MobilityModuleEmailScenarioContentSpecs';
import { MobilityCustomerEmailContentDataFragmentDoc } from '../fragments/MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from '../fragments/MobilityEmailContentData';
import { MobilityOperatorEmailContentDataFragmentDoc } from '../fragments/MobilityOperatorEmailContentData';
import { MobilityHomeDeliveryDataFragmentDoc } from '../fragments/MobilityHomeDeliveryData';
import { LabelsModuleSpecsFragmentDoc } from '../fragments/LabelsModuleSpecs';
import { FinderVehicleManagementModuleSpecsFragmentDoc } from '../fragments/FinderVehicleManagementModuleSpecs';
import { FinderApplicationPublicModuleSpecsFragmentDoc } from '../fragments/FinderApplicationPublicModuleSpecs';
import { FinderApplicationModuleEmailContentSpecsFragmentDoc } from '../fragments/FinderApplicationModuleEmailContentSpecs';
import { ModuleDisclaimersDataFragmentDoc } from '../fragments/ModuleDisclaimersData';
import { FinderApplicationPrivateModuleSpecsFragmentDoc } from '../fragments/FinderApplicationPrivateModuleSpecs';
import { AutoplayModuleSpecsFragmentDoc } from '../fragments/AutoplayModuleSpecs';
import { AutoplaySettingSpecsFragmentDoc } from '../fragments/AutoplaySettingSpecs';
import { CtsModuleSpecsFragmentDoc } from '../fragments/CtsModuleSpecs';
import { CtsModuleSettingDataFragmentDoc } from '../fragments/CtsModuleSettingData';
import { AppointmentModuleSpecsFragmentDoc } from '../fragments/AppointmentModuleSpecs';
import {
    AppointmentModuleEmailContentsSpecsFragmentDoc,
    AppointmentModuleEmailContentCustomerSpecsFragmentDoc,
    AppointmentModuleEmailContentSpecsFragmentDoc,
    AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc,
    AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc,
} from '../fragments/AppointmentModuleEmailContentsSpecs';
import { InsuranceModuleSpecsFragmentDoc } from '../fragments/InsuranceModuleSpecs';
import { PorscheMasterDataModuleSpecsFragmentDoc } from '../fragments/PorscheMasterDataModuleSpecs';
import { GiftVoucherModuleSpecsFragmentDoc } from '../fragments/GiftVoucherModuleSpecs';
import {
    GiftVoucherModuleEmailContentsSpecsFragmentDoc,
    GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc,
    GiftVoucherModuleEmailDataFragmentDoc,
} from '../fragments/GiftVoucherModuleEmailContentsSpecs';
import { TradeInModuleSpecsFragmentDoc } from '../fragments/TradeInModuleSpecs';
import { TradeInSettingSpecFragmentDoc } from '../fragments/TradeInSetting';
import { CapModuleSpecsFragmentDoc } from '../fragments/CapModuleSpecs';
import { CapSettingSpecFragmentDoc } from '../fragments/CapSettingSpec';
import { PorscheIdModuleSpecsFragmentDoc } from '../fragments/PorscheIdModuleSpecs';
import { PorscheIdSettingSpecFragmentDoc } from '../fragments/PorscheIdSettingSpec';
import { PorscheRetainModuleSpecsFragmentDoc } from '../fragments/PorscheRetainModuleSpecs';
import { DocusignModuleSpecsFragmentDoc } from '../fragments/DocusignModuleSpecs';
import { DocusignSettingDataFragmentDoc } from '../fragments/DocusignSettingData';
import { LaunchPadModuleSpecsFragmentDoc } from '../fragments/LaunchPadModuleSpecs';
import { VisitAppointmentModuleSpecsFragmentDoc } from '../fragments/VisitAppointmentModuleSpecs';
import { TimeSlotDataFragmentDoc } from '../fragments/TimeSlotData';
import {
    VisitAppointmentModuleEmailContentsSpecsFragmentDoc,
    VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc,
    VisitAppointmentModuleEmailContentSpecsFragmentDoc,
    VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc,
} from '../fragments/VisitAppointmentModuleEmailContentsSpecs';
import { OidcModuleSpecsFragmentDoc } from '../fragments/OIDCModuleSpecs';
import { MarketingModuleSpecsFragmentDoc } from '../fragments/MarketingModuleSpecs';
import { SalesOfferModuleSpecsFragmentDoc } from '../fragments/SalesOfferModuleSpecs';
import { BankDetailsDataFragmentDoc } from '../fragments/BankDetailsData';
import { TranslatedStringSpecsFragmentDoc } from '../fragments/TranslatedStringSpecs';
import { BankIntegrationDataFragmentDoc } from '../fragments/BankIntegrationData';
import { UploadFileFormDataFragmentDoc } from '../fragments/UploadFileFormData';
import { FinanceProductDetailsDataFragmentDoc } from '../fragments/FinanceProductDetailsData';
import { PaymentSettingsDetailsFragmentDoc } from '../fragments/PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from '../fragments/LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from '../fragments/TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from '../fragments/InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from '../fragments/DownPaymentSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from '../fragments/LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from '../fragments/DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from '../fragments/ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from '../fragments/LocalUcclLeasingOnlyDetails';
import {
    SalesOfferModuleEmailContentsSpecsFragmentDoc,
    SalesOfferEmailContentsSpecsFragmentDoc,
} from '../fragments/SalesOfferModuleEmailContentsSpecs';
import { VehicleDataWithPorscheCodeIntegrationModuleSpecsFragmentDoc } from '../fragments/VehicleDataWithPorscheCodeIntegrationModuleSpecs';
import { SalesControlBoardModuleSpecsFragmentDoc } from '../fragments/SalesControlBoardModuleSpecs';
import {
    DealerIntDataFragmentDoc,
    DealerFloatDataFragmentDoc,
    DealerObjectIdDataFragmentDoc,
} from '../fragments/DealerIntData';
import { OfrModuleSpecsFragmentDoc } from '../fragments/OFRModuleSpecs';
import {
    OfrModuleEmailContentsSpecsFragmentDoc,
    OfrSalesConsultantEmailContentSpecsFragmentDoc,
    OfrSalesConsultantEmailContentContextSpecsFragmentDoc,
    OfrCustomerEmailContentSpecsFragmentDoc,
    OfrCustomerEmailContentContextSpecsFragmentDoc,
} from '../fragments/OFRModuleEmailContentsSpecs';
import { OfrEquityDataFragmentDoc } from '../fragments/OFREquityData';
import { ModuleVariantDataFragmentDoc } from '../fragments/ModuleVariantData';
import { LocalVariantSpecsFragmentDoc } from '../fragments/LocalVariantSpecs';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListLabelsQueryVariables = SchemaTypes.Exact<{
    pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
    sort?: SchemaTypes.InputMaybe<SchemaTypes.LabelsSortingRule>;
    filter?: SchemaTypes.InputMaybe<SchemaTypes.LabelsFilteringField>;
}>;

export type ListLabelsQuery = { __typename: 'Query' } & {
    labels: { __typename: 'PaginatedLabels' } & Pick<SchemaTypes.PaginatedLabels, 'count'> & {
            items: Array<{ __typename: 'Labels' } & LabelsDataFragment>;
        };
};

export const ListLabelsDocument = /*#__PURE__*/ gql`
    query listLabels($pagination: Pagination, $sort: LabelsSortingRule, $filter: LabelsFilteringField) {
        labels: listLabels(pagination: $pagination, sort: $sort, filter: $filter) {
            count
            items {
                ...LabelsData
            }
        }
    }
    ${LabelsDataFragmentDoc}
    ${ModuleSpecsFragmentDoc}
    ${ConsentsAndDeclarationsModuleSpecsFragmentDoc}
    ${SimpleVersioningDataFragmentDoc}
    ${AuthorDataFragmentDoc}
    ${SimpleVehicleManagementModuleSpecsFragmentDoc}
    ${CompanyInModuleOptionDataFragmentDoc}
    ${VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc}
    ${LocalCustomerManagementModuleSpecsFragmentDoc}
    ${LocalCustomerManagementModuleKycFieldSpecsFragmentDoc}
    ${KycExtraSettingsSpecsFragmentDoc}
    ${KycPresetsSpecFragmentDoc}
    ${ConditionSpecsFragmentDoc}
    ${BaseConditionSpecsFragmentDoc}
    ${MobilityLocationDataFragmentDoc}
    ${UserPreviewDataFragmentDoc}
    ${BankModuleSpecsFragmentDoc}
    ${BasicSigningModuleSpecsFragmentDoc}
    ${NamirialSigningModuleSpecsFragmentDoc}
    ${NamirialSettingsSpecFragmentDoc}
    ${StandardApplicationModuleSpecsFragmentDoc}
    ${DealerPriceDisclaimerDataFragmentDoc}
    ${TranslatedStringDataFragmentDoc}
    ${DepositAmountDataFragmentDoc}
    ${ApplicationMarketTypeFragmentFragmentDoc}
    ${DealerMarketDataFragmentDoc}
    ${BankDealerMarketDataFragmentDoc}
    ${NzFeesDealerMarketDataFragmentDoc}
    ${DealerVehiclesSpecsFragmentDoc}
    ${DealerFinanceProductsSpecsFragmentDoc}
    ${FinanceProductListDataFragmentDoc}
    ${PeriodDataFragmentDoc}
    ${ModulesCompanyTimezoneDataFragmentDoc}
    ${VehicleReferenceParametersDataFragmentDoc}
    ${BalloonSettingsDetailsFragmentDoc}
    ${BalloonGfvSettingsDetailsFragmentDoc}
    ${DealerInsuranceProductsSpecsFragmentDoc}
    ${InsuranceProductListDataFragmentDoc}
    ${ErgoLookupTableSettingsDetailsFragmentDoc}
    ${LocalModelSpecsFragmentDoc}
    ${LocalMakeSpecsFragmentDoc}
    ${AdvancedVersioningDataFragmentDoc}
    ${DealerDisclaimersConfiguratorDataFragmentDoc}
    ${KycPresetsOptionsDataFragmentDoc}
    ${DealershipSettingSpecDataFragmentDoc}
    ${FlexibleDiscountDataFragmentDoc}
    ${CounterSettingsSpecsFragmentDoc}
    ${StandardApplicationModuleEmailContentsSpecsFragmentDoc}
    ${StandardApplicationModuleEmailContentCustomerSpecsFragmentDoc}
    ${StandardApplicationModuleEmailContentShareSubmissionSpecsFragmentDoc}
    ${DealerTranslatedStringSettingDataFragmentDoc}
    ${DealerUploadedFileWithPreviewDataFragmentDoc}
    ${UploadFileWithPreviewFormDataFragmentDoc}
    ${DealerBooleanSettingDataFragmentDoc}
    ${StandardApplicationModuleEmailContentSpecsFragmentDoc}
    ${StandardApplicationModuleEmailContentSalesPersonSpecsFragmentDoc}
    ${EventApplicationModuleSpecsFragmentDoc}
    ${AppointmentModuleOnEventModuleDataFragmentDoc}
    ${AppointmentTimeSlotDataFragmentDoc}
    ${EventApplicationModuleEmailContentSpecsFragmentDoc}
    ${TranslatedTextDataFragmentDoc}
    ${AdyenPaymentModuleSpecsFragmentDoc}
    ${AdyenPaymentSettingsSpecFragmentDoc}
    ${PorschePaymentModuleSpecsFragmentDoc}
    ${PorschePaymentSettingsSpecFragmentDoc}
    ${FiservPaymentModuleSpecsFragmentDoc}
    ${FiservPaymentSettingsSpecFragmentDoc}
    ${PayGatePaymentModuleSpecsFragmentDoc}
    ${PayGatePaymentSettingsSpecFragmentDoc}
    ${TtbPaymentModuleSpecsFragmentDoc}
    ${TtbPaymentSettingsSpecFragmentDoc}
    ${MyInfoModuleSpecsFragmentDoc}
    ${MyInfoSettingSpecFragmentDoc}
    ${ConfiguratorModuleSpecsFragmentDoc}
    ${ConfiguratorModuleEmailContentSpecsFragmentDoc}
    ${WhatsappLiveChatModuleSpecsFragmentDoc}
    ${WhatsappLiveChatSettingsSpecFragmentDoc}
    ${UserlikeChatbotModuleSpecsFragmentDoc}
    ${UserlikeChatbotSettingsSpecFragmentDoc}
    ${PromoCodeModuleSpecsFragmentDoc}
    ${MaintenanceModuleSpecsFragmentDoc}
    ${WebsiteModuleSpecsFragmentDoc}
    ${EdmSocialMediaDataFragmentDoc}
    ${MobilityModuleSpecsFragmentDoc}
    ${DealerBookingCodeSpecsFragmentDoc}
    ${MobilitySigningSettingSpecsFragmentDoc}
    ${MobilityModuleEmailScenarioContentSpecsFragmentDoc}
    ${MobilityCustomerEmailContentDataFragmentDoc}
    ${MobilityEmailContentDataFragmentDoc}
    ${MobilityOperatorEmailContentDataFragmentDoc}
    ${MobilityHomeDeliveryDataFragmentDoc}
    ${LabelsModuleSpecsFragmentDoc}
    ${FinderVehicleManagementModuleSpecsFragmentDoc}
    ${FinderApplicationPublicModuleSpecsFragmentDoc}
    ${FinderApplicationModuleEmailContentSpecsFragmentDoc}
    ${ModuleDisclaimersDataFragmentDoc}
    ${FinderApplicationPrivateModuleSpecsFragmentDoc}
    ${AutoplayModuleSpecsFragmentDoc}
    ${AutoplaySettingSpecsFragmentDoc}
    ${CtsModuleSpecsFragmentDoc}
    ${CtsModuleSettingDataFragmentDoc}
    ${AppointmentModuleSpecsFragmentDoc}
    ${AppointmentModuleEmailContentsSpecsFragmentDoc}
    ${AppointmentModuleEmailContentCustomerSpecsFragmentDoc}
    ${AppointmentModuleEmailContentSpecsFragmentDoc}
    ${AppointmentModuleEmailContentSalesPersonSpecsFragmentDoc}
    ${AppointmentModuleEmailContentFinderReservationSpecsFragmentDoc}
    ${InsuranceModuleSpecsFragmentDoc}
    ${PorscheMasterDataModuleSpecsFragmentDoc}
    ${GiftVoucherModuleSpecsFragmentDoc}
    ${GiftVoucherModuleEmailContentsSpecsFragmentDoc}
    ${GiftVoucherModuleEmailContentCustomerSpecsFragmentDoc}
    ${GiftVoucherModuleEmailDataFragmentDoc}
    ${TradeInModuleSpecsFragmentDoc}
    ${TradeInSettingSpecFragmentDoc}
    ${CapModuleSpecsFragmentDoc}
    ${CapSettingSpecFragmentDoc}
    ${PorscheIdModuleSpecsFragmentDoc}
    ${PorscheIdSettingSpecFragmentDoc}
    ${PorscheRetainModuleSpecsFragmentDoc}
    ${DocusignModuleSpecsFragmentDoc}
    ${DocusignSettingDataFragmentDoc}
    ${LaunchPadModuleSpecsFragmentDoc}
    ${VisitAppointmentModuleSpecsFragmentDoc}
    ${TimeSlotDataFragmentDoc}
    ${VisitAppointmentModuleEmailContentsSpecsFragmentDoc}
    ${VisitAppointmentModuleEmailContentCustomerSpecsFragmentDoc}
    ${VisitAppointmentModuleEmailContentSpecsFragmentDoc}
    ${VisitAppointmentModuleEmailContentSalesPersonSpecsFragmentDoc}
    ${OidcModuleSpecsFragmentDoc}
    ${MarketingModuleSpecsFragmentDoc}
    ${SalesOfferModuleSpecsFragmentDoc}
    ${BankDetailsDataFragmentDoc}
    ${TranslatedStringSpecsFragmentDoc}
    ${BankIntegrationDataFragmentDoc}
    ${UploadFileFormDataFragmentDoc}
    ${FinanceProductDetailsDataFragmentDoc}
    ${PaymentSettingsDetailsFragmentDoc}
    ${LoanSettingsDetailsFragmentDoc}
    ${TermSettingsDetailsFragmentDoc}
    ${InterestRateSettingsDetailsFragmentDoc}
    ${DownPaymentSettingsDetailsFragmentDoc}
    ${LeaseSettingsDetailsFragmentDoc}
    ${DepositSettingsDetailsFragmentDoc}
    ${ResidualValueSettingsDetailsFragmentDoc}
    ${LocalUcclLeasingOnlyDetailsFragmentDoc}
    ${SalesOfferModuleEmailContentsSpecsFragmentDoc}
    ${SalesOfferEmailContentsSpecsFragmentDoc}
    ${VehicleDataWithPorscheCodeIntegrationModuleSpecsFragmentDoc}
    ${SalesControlBoardModuleSpecsFragmentDoc}
    ${DealerIntDataFragmentDoc}
    ${DealerFloatDataFragmentDoc}
    ${DealerObjectIdDataFragmentDoc}
    ${OfrModuleSpecsFragmentDoc}
    ${OfrModuleEmailContentsSpecsFragmentDoc}
    ${OfrSalesConsultantEmailContentSpecsFragmentDoc}
    ${OfrSalesConsultantEmailContentContextSpecsFragmentDoc}
    ${OfrCustomerEmailContentSpecsFragmentDoc}
    ${OfrCustomerEmailContentContextSpecsFragmentDoc}
    ${OfrEquityDataFragmentDoc}
    ${ModuleVariantDataFragmentDoc}
    ${LocalVariantSpecsFragmentDoc}
`;

/**
 * __useListLabelsQuery__
 *
 * To run a query within a React component, call `useListLabelsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListLabelsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListLabelsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListLabelsQuery(baseOptions?: Apollo.QueryHookOptions<ListLabelsQuery, ListLabelsQueryVariables>) {
    const options = { ...defaultOptions, ...baseOptions };
    return Apollo.useQuery<ListLabelsQuery, ListLabelsQueryVariables>(ListLabelsDocument, options);
}
export function useListLabelsLazyQuery(
    baseOptions?: Apollo.LazyQueryHookOptions<ListLabelsQuery, ListLabelsQueryVariables>
) {
    const options = { ...defaultOptions, ...baseOptions };
    return Apollo.useLazyQuery<ListLabelsQuery, ListLabelsQueryVariables>(ListLabelsDocument, options);
}
export type ListLabelsQueryHookResult = ReturnType<typeof useListLabelsQuery>;
export type ListLabelsLazyQueryHookResult = ReturnType<typeof useListLabelsLazyQuery>;
export type ListLabelsQueryResult = Apollo.QueryResult<ListLabelsQuery, ListLabelsQueryVariables>;
