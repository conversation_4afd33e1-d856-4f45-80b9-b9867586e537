import dayjs from 'dayjs';
import saveAs from 'file-saver';

type ExportTableParams = {
    token: string;
    moduleId: string;
    dealerId?: string;
};

const exportOfrRvTable = async ({ token, moduleId, dealerId }: ExportTableParams) => {
    const headers = {
        'Content-Type': 'application/json',
        'X-Timezone': dayjs.tz.guess(),
        Authorization: `Bearer ${token}`,
    };

    const response = await fetch('/api/export/ofrRVTable', {
        method: 'POST',
        headers,
        body: JSON.stringify({ moduleId, dealerId }),
    });

    if (response?.ok) {
        const blob = await response.blob();

        return saveAs(blob, dealerId ? `Dealer-OFRRVTable.xlsx` : `Module-OFRRVTable.xlsx`);
    }

    if (response?.status === 404) {
        const error = await response.text();
        throw new Error(error);
    }

    return null;
};

export default exportOfrRvTable;
