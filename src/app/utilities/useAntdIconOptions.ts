import {
    AlertOutlined,
    FileAddFilled,
    FileOutlined,
    CalendarFilled,
    AccountBookFilled,
    AccountBookOutlined,
    LinkedinFilled,
    FileDoneOutlined,
    CalendarOutlined,
    CameraFilled,
    CarFilled,
    CarOutlined,
    FileExcelOutlined,
    BoxPlotOutlined,
    TabletOutlined,
    SoundOutlined,
    ProfileOutlined,
    LayoutOutlined,
    HomeOutlined,
    BellOutlined,
    FileWordOutlined,
    FileTextFilled,
} from '@ant-design/icons';
import { useMemo } from 'react';

export const MENU_ITEM_ICON_MAP = {
    AlertOutlined,
    FileAddFilled,
    FileOutlined,
    CalendarFilled,
    AccountBookFilled,
    AccountBookOutlined,
    LinkedinFilled,
    FileDoneOutlined,
    CalendarOutlined,
    CameraFilled,
    CarFilled,
    CarOutlined,
    FileExcelOutlined,
    BoxPlotOutlined,
    TabletOutlined,
    SoundOutlined,
    ProfileOutlined,
    LayoutOutlined,
    HomeOutlined,
    BellOutlined,
    FileWordOutlined,
    FileTextFilled,
};

const ALLOWED_ICON_NAMES = Object.keys(MENU_ITEM_ICON_MAP);

type AntdIconOption = { label: string; value: string };

const useAntdIconOptions = () =>
    useMemo<AntdIconOption[]>(() => ALLOWED_ICON_NAMES.map(name => ({ label: name, value: name })), []);

export default useAntdIconOptions;
