import type { InputRef } from 'antd';
import { Table } from 'antd';
import type { ColumnFilterItem, FilterDropdownProps } from 'antd/es/table/interface';
import { getOr, isNil, pick, sortBy } from 'lodash/fp';
import type { Key } from 'react';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import type { ApplicationListDataFragment } from '../../../api/fragments/ApplicationListData';
import { useGetMobilityApplicationsListFiltersQuery } from '../../../api/queries/getMobilityApplicationsListFilters';
import { useListApplicationsQuery } from '../../../api/queries/listApplications';
import { ApplicationKind, ApplicationSortingField, ApplicationStage, ApplicationStatus } from '../../../api/types';
import FilterBox from '../../../components/FilterBox';
import type { PaginatedTableProps } from '../../../components/PaginatedTable';
import PaginatedTableWithContext from '../../../components/PaginatedTableWithContext';
import RangeDateFilterBox, { objectToRangeDateValue } from '../../../components/RangeDateFilterBox';
import SearchBox from '../../../components/SearchBox';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import { useMultipleDealerIds } from '../../../components/contexts/DealerContextManager';
import { getDateTimeOffsetFormat } from '../../../utilities/date';
import makeGetSortingRule from '../../../utilities/makeGetSortingRule';
import renderFilterIcon from '../../../utilities/renderFilterIcon';
import renderSearchIcon from '../../../utilities/renderSearchIcon';
import useFormatDate from '../../../utilities/useFormatDate';
import useFormats from '../../../utilities/useFormats';
import useSortAndFilterCache, { SortAndFilterCacheKey } from '../../../utilities/useSortAndFilterCache';
import useTranslatedString from '../../../utilities/useTranslatedString';
import { renderStatusTag } from '../../shared/ApplicationList';
import useSortOrder from '../../shared/ApplicationList/useSortOrder';
import PriceFeatureLabel from '../../shared/PriceFeatureLabel';
import useListReducer from './useListReducer';
import useVariantFilter from './useVariantFilter';

export const getSortField = makeGetSortingRule((field): ApplicationSortingField => {
    switch (field) {
        case 'mobilityStage.identifier':
            return ApplicationSortingField.Identifier;

        case 'module.company.displayName':
            return ApplicationSortingField.Company;

        case 'mobilityBookingDetails.period.start':
            return ApplicationSortingField.BookingStartDate;

        case 'mobilityBookingDetails.period.end':
            return ApplicationSortingField.BookingEndDate;

        case 'mobilityBookingDetails.location.name':
            return ApplicationSortingField.BookingLocation;

        case 'vehicle.identifier':
            return ApplicationSortingField.Vehicle;

        case 'vehicle.name':
            return ApplicationSortingField.VehicleName;

        case 'applicant.fullName':
            return ApplicationSortingField.Customer;

        case 'mobilityStage.status':
            return ApplicationSortingField.ApplicationStatus;

        case 'deposit.amount':
            return ApplicationSortingField.TotalAmountPaid;

        case 'versioning.createdAt':
            return ApplicationSortingField.BookingSubmitted;

        default:
            throw new Error('Such Field is not supported');
    }
});

const getDefaultStatusFilter = list =>
    list.filter(status => ApplicationStatus.NewMobility.includes(status.value)).map(status => status.value);

export type ApplicationDataSource = ApplicationListDataFragment & { key: Key };

export type PaginatedApplicationTable = PaginatedTableProps<ApplicationDataSource>;

export type MobilityBookingListProps = {
    moduleIds: string[];
};

const MobilityBookingList = ({ moduleIds }: MobilityBookingListProps) => {
    const { t } = useTranslation(['applicationList', 'common', 'inventoryDetails']);
    const translate = useTranslatedString();

    const [currentCache, setCache] = useSortAndFilterCache(SortAndFilterCacheKey.MobilityBooking);

    const inputRef = useRef<InputRef>(null);
    const navigate = useNavigate();
    const [state, dispatch] = useListReducer(currentCache);
    const { page, pageSize, sort, filter } = state;

    const company = useCompany(true);
    const formatDate = useFormatDate();
    const { dealerIds } = useMultipleDealerIds();

    const { data } = useListApplicationsQuery({
        nextFetchPolicy: 'cache-and-network',
        variables: {
            pagination: { offset: (page - 1) * pageSize, limit: pageSize },
            sort: pick(['field', 'order'], sort),
            filter: {
                ...filter,
                kind: ApplicationKind.Mobility,
                dealerIds,
                moduleIds,
                stage: ApplicationStage.Mobility,
            },
        },
    });

    const { data: appFilterList } = useGetMobilityApplicationsListFiltersQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            filter: { kind: ApplicationKind.Mobility, dealerIds, moduleIds, stage: ApplicationStage.Mobility },
        },
    });

    const [statusList, companyList, locationFilter] = useMemo(() => {
        const filterList = appFilterList?.getMobilityApplicationsListFilters;
        if (!filterList) {
            return [[], [], []];
        }
        const statusList = sortBy(
            'text',
            filterList.applicationStatus.map(status => ({
                text: t(`applicationList:status.${status}`),
                value: status,
            }))
        );

        const commpanyList = sortBy(
            'text',
            filterList.companies.map(company => ({
                text: company.displayName,
                value: company.id,
            }))
        );

        const locationsListOptions = sortBy(
            'text',
            filterList.mobilityLocations.map(location => {
                if (location.__typename === 'MobilityBookingLocationHome') {
                    return { text: t('common:homeDelivery'), value: 'homeDelivery' };
                }

                return { text: location.name, value: location.name };
            })
        );

        return [statusList, commpanyList, locationsListOptions];
    }, [appFilterList, t]);

    // set default filter for status
    const isStatusInitialized = useRef(!(!currentCache?.filter?.status || !state?.filter?.status));
    useEffect(() => {
        if (statusList.length && !isStatusInitialized.current) {
            const defaultStatusFilter = getDefaultStatusFilter(statusList);

            if (defaultStatusFilter.length) {
                isStatusInitialized.current = true;
                dispatch({
                    type: 'setFilter',
                    filteringRule: {
                        ...filter,
                        status: defaultStatusFilter,
                    },
                });
            }
        }
    }, [statusList, currentCache, dispatch, filter]);

    const dataSource = useMemo(
        () =>
            ((data?.list?.items as ApplicationListDataFragment[]) || []).map(item => ({
                ...item,
                key: item.id,
                mobilityBookingDetails:
                    item.__typename === 'MobilityApplication'
                        ? {
                              ...item.mobilityBookingDetails,
                              location: {
                                  ...item.mobilityBookingDetails.location,
                                  name:
                                      item.mobilityBookingDetails.location?.__typename === 'MobilityBookingLocationHome'
                                          ? t('common:homeDelivery')
                                          : item.mobilityBookingDetails.location?.name,
                              },
                          }
                        : {},
            })),
        [data, t]
    );
    const total = data?.list?.count || 0;

    const showSearchDropDown = useCallback(
        (props: FilterDropdownProps, dataIndex: string) =>
            SearchBox({
                filterDropDownProps: props,
                dataIndex,
                onRef: node => {
                    inputRef.current = node;
                },
            }),
        [inputRef]
    );

    const showFilterDropDown = useCallback(
        (props: FilterDropdownProps, filters: ColumnFilterItem[]) =>
            FilterBox({
                filterDropDownProps: props,
                filters,
            }),
        []
    );

    // Override reset status when clicking "Reset" inside dropdown filter statuses
    // From clear all filters, into only set selected default statuses
    const onResetStatusFilters = useCallback(
        ({ setSelectedKeys, clearFilters }: FilterDropdownProps): FilterDropdownProps['clearFilters'] =>
            () => {
                const defaultStatusFilters = getDefaultStatusFilter(statusList);
                if (defaultStatusFilters.length) {
                    setSelectedKeys(defaultStatusFilters);
                } else {
                    clearFilters();
                }
            },
        [statusList]
    );

    const showRangeFilterDropDown = useCallback(
        (props: FilterDropdownProps) =>
            RangeDateFilterBox({
                filterDropDownProps: props,
                picker: 'date',
            }),
        []
    );

    const onChange = useCallback<PaginatedApplicationTable['onChange']>(
        (pagination, filters, sorter, extra) => {
            switch (extra.action) {
                case 'sort': {
                    return dispatch({
                        type: 'setSort',
                        sortBy: getSortField(sorter),
                    });
                }

                case 'filter': {
                    return dispatch({
                        type: 'setFilter',
                        filteringRule: {
                            identifier: !filters.identifier ? undefined : (filters.identifier[0] as string),
                            companyIds: !filters.company ? undefined : (filters.company as string[]),
                            vehicleNames: !filters.vehicle ? undefined : (filters.vehicle as string[]),
                            locations: !filters.location ? undefined : (filters.location as string[]),
                            vehicle: !filters.vehicleId ? undefined : (filters.vehicleId[0] as string),
                            customer: !filters.applicant ? undefined : (filters.applicant[0] as string),
                            status: !filters.status ? undefined : (filters.status as ApplicationStatus[]),
                            vin: !filters.vin ? undefined : (filters.vin[0] as string),
                            startDateRange: !filters.startDate
                                ? undefined
                                : {
                                      start: new Date(filters.startDate[0] as string),
                                      end: new Date(filters.startDate[1] as string),
                                  },
                            endDateRange: !filters.endDate
                                ? undefined
                                : {
                                      start: new Date(filters.endDate[0] as string),
                                      end: new Date(filters.endDate[1] as string),
                                  },
                            createdDateRange: !filters.bookingSubmitted
                                ? undefined
                                : {
                                      start: new Date(filters.bookingSubmitted[0] as string),
                                      end: new Date(filters.bookingSubmitted[1] as string),
                                  },
                        },
                    });
                }

                default:
                    return undefined;
            }
        },
        [dispatch]
    );

    const onRow = useCallback<PaginatedApplicationTable['onRow']>(
        record => ({
            onClick: () => {
                setCache(state);
                navigate(record.versioning.suiteId);
            },
        }),
        [navigate, setCache, state]
    );

    const vehicleNameFilter = useVariantFilter();
    const { formatAmountWithCurrency } = useFormats(
        company?.currency || '',
        company?.roundings?.amount?.decimals || 0,
        company?.roundings?.percentage?.decimals || 0
    );

    const {
        companySort,
        appDateSort,
        identifierSort,
        customerSort,
        vehicleNameSort,
        bookingStartDateSort,
        bookingEndDateSort,
        bookingLocationSort,
        totalAmountPaidSort,
        applicationStatusSort,
    } = useSortOrder(sort);

    return (
        <PaginatedTableWithContext
            company={company}
            dataSource={dataSource}
            dispatch={dispatch}
            onChange={onChange}
            onRow={onRow}
            rowKey="id"
            scroll={{ x: true }}
            state={state}
            tableName={t('inventoryDetails:sections.mobilityBookings')}
            total={total}
        >
            {!company && (
                <Table.Column
                    key="company"
                    dataIndex={['module', 'company', 'displayName']}
                    filterDropdown={props => showFilterDropDown(props, companyList)}
                    filterIcon={renderFilterIcon}
                    filteredValue={filter.companyIds}
                    sortOrder={companySort}
                    title={t('applicationList:columns.company')}
                    sorter
                />
            )}
            <Table.Column
                key="identifier"
                dataIndex={['mobilityStage', 'identifier']}
                filterDropdown={props => showSearchDropDown(props, 'identifier')}
                filterIcon={renderSearchIcon}
                filteredValue={filter.identifier ? [filter.identifier] : undefined}
                sortOrder={identifierSort}
                title={t('applicationList:columns.bookingId')}
                sorter
            />
            <Table.Column
                key="bookingSubmitted"
                dataIndex={['versioning', 'createdAt']}
                filterDropdown={props => showRangeFilterDropDown(props)}
                filteredValue={objectToRangeDateValue(filter.createdDateRange)}
                render={(value, record: ApplicationListDataFragment) =>
                    !isNil(value)
                        ? formatDate({
                              date: value,
                              timeZone: getOr(company?.timeZone, 'module.company.timeZone', record),
                              withOffset: true,
                          })
                        : ''
                }
                sortOrder={appDateSort}
                title={t('applicationList:columns.bookingSubmitted')}
                sorter
            />
            <Table.Column
                key="dealerDisplayName"
                dataIndex={['dealer', 'displayName']}
                title={t('applicationList:columns.dealer')}
            />
            <Table.Column
                key="startDate"
                dataIndex={['mobilityBookingDetails', 'period', 'start']}
                filterDropdown={props => showRangeFilterDropDown(props)}
                filteredValue={objectToRangeDateValue(filter.startDateRange)}
                render={(value, record: ApplicationListDataFragment) =>
                    getDateTimeOffsetFormat(value, t, record.module.company.timeZone)
                }
                sortOrder={bookingStartDateSort}
                title={t('applicationList:columns.startDateTime')}
                sorter
            />
            <Table.Column
                key="endDate"
                dataIndex={['mobilityBookingDetails', 'period', 'end']}
                filterDropdown={props => showRangeFilterDropDown(props)}
                filteredValue={objectToRangeDateValue(filter.endDateRange)}
                render={(value, record: ApplicationListDataFragment) =>
                    getDateTimeOffsetFormat(value, t, record.module.company.timeZone)
                }
                sortOrder={bookingEndDateSort}
                title={t('applicationList:columns.endDateTime')}
                sorter
            />
            <Table.Column
                key="applicant"
                dataIndex={['applicant', 'fullName']}
                filterDropdown={props => showSearchDropDown(props, 'applicant')}
                filterIcon={renderSearchIcon}
                filteredValue={filter.customer ? [filter.customer] : undefined}
                sortOrder={customerSort}
                title={t('applicationList:columns.customer')}
                sorter
            />
            <Table.Column
                key="totalAmountPaid"
                dataIndex={['deposit', 'amount']}
                render={(value, record: ApplicationListDataFragment) => (
                    <PriceFeatureLabel
                        currency={record.module.company.currency}
                        decimals={record.module.company.roundings.amount.decimals}
                        percentages={record.module.company.roundings.percentage.decimals}
                        value={value}
                    />
                )}
                sortOrder={totalAmountPaidSort}
                title={t('applicationList:columns.totalAmountPaid')}
                sorter
            />
            <Table.Column
                key="vehicle"
                dataIndex={['vehicle', 'name']}
                filterDropdown={props => showFilterDropDown(props, vehicleNameFilter)}
                filterIcon={renderFilterIcon}
                filteredValue={filter.vehicleNames}
                render={value => translate(value)}
                sortOrder={vehicleNameSort}
                title={t('applicationList:columns.vehicle')}
                sorter
            />
            <Table.Column
                key="vin"
                dataIndex={['vin']}
                filterDropdown={props => showSearchDropDown(props, 'vin')}
                filterIcon={renderSearchIcon}
                filteredValue={filter.vin ? [filter.vin] : undefined}
                title={t('applicationList:columns.vin')}
            />
            <Table.Column
                key="location"
                dataIndex={['mobilityBookingDetails', 'location', 'name']}
                filterDropdown={props => showFilterDropDown(props, locationFilter)}
                filterIcon={renderFilterIcon}
                filteredValue={filter.locations}
                sortOrder={bookingLocationSort}
                title={t('applicationList:columns.location')}
                sorter
            />
            <Table.Column
                key="status"
                dataIndex={['mobilityStage', 'status']}
                filterDropdown={props =>
                    showFilterDropDown(
                        {
                            ...props,
                            clearFilters: onResetStatusFilters(props),
                        },
                        statusList
                    )
                }
                filterIcon={renderFilterIcon}
                filteredValue={filter.status}
                onFilter={(value: string, record: ApplicationListDataFragment) =>
                    record.mobilityStage?.status.indexOf(value) === 0
                }
                render={value => renderStatusTag(value, ApplicationStage.Mobility, t)}
                sortOrder={applicationStatusSort}
                title={t('applicationList:columns.status')}
                sorter
            />
        </PaginatedTableWithContext>
    );
};

export default MobilityBookingList;
