import { InfoCircleTwoTone } from '@ant-design/icons';
import Icon from '@ant-design/icons/es/components/Icon';
import type { ColumnsType } from 'antd/lib/table';
import type { TFunction } from 'i18next';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router';
import styled from 'styled-components';
import type { ApplicationListDataFragment } from '../../../../api/fragments/ApplicationListData';
import type { ModuleWithScenariosFragment } from '../../../../api/fragments/ModuleWithScenarios';
import { useGetModulesWithScenariosQuery } from '../../../../api/queries/getModulesWithScenarios';
import { useListApplicationsQuery } from '../../../../api/queries/listApplications';
import { ApplicationSortingField, ApplicationStage, ModuleRole, SortingOrder } from '../../../../api/types';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { useMultipleDealerIds } from '../../../../components/contexts/DealerContextManager';
import { renderStatusTag } from '../../../shared/ApplicationList';
import { hasFinancingScenario } from '../../ModuleDetailsPage/modules/implementations/shared/scenarios';

const renderAppID = (value: ApplicationListDataFragment) => (
    <Link to={`/admin/applications/${value.versioning.suiteId}`}>{value.financingStage?.identifier}</Link>
);

const renderDate = (value: string, t: TFunction) =>
    t('common:formats.dateTime', {
        date: new Date(value),
    });

const InformationIcon = styled(Icon)`
    &:hover {
        cursor: pointer;
    }
`;

const statusHeader = (t: TFunction, onStatusIconClicked: () => void) => (
    <>
        <span style={{ marginRight: '0.5rem' }}>{t('dashboard:latestApplicationTable.tableColumns.status.title')}</span>
        <InformationIcon component={InfoCircleTwoTone} onClick={onStatusIconClicked} />
    </>
);

const columns = (t: TFunction, onStatusIconClicked: () => void): ColumnsType<ApplicationListDataFragment> => [
    {
        ...t('dashboard:latestApplicationTable.tableColumns.applicationDate', { returnObjects: true }),
        dataIndex: ['versioning', 'updatedAt'],
        key: 'applicationDate',
        render: value => renderDate(value, t),
        fixed: true,
    },
    {
        ...t('dashboard:latestApplicationTable.tableColumns.appID', { returnObjects: true }),
        key: 'identifier',
        render: renderAppID,
    },
    {
        ...t('dashboard:latestApplicationTable.tableColumns.customer', { returnObjects: true }),
        dataIndex: ['applicant', 'fullName'],
        key: 'applicant',
    },
    {
        ...t('dashboard:latestApplicationTable.tableColumns.variant', { returnObjects: true }),
        dataIndex: ['vehicle', 'identifier'],
        key: 'variant',
    },
    {
        ...t('dashboard:latestApplicationTable.tableColumns.bank', { returnObjects: true }),
        dataIndex: ['bank', 'displayName'],
        key: 'bankName',
    },
    {
        ...t('dashboard:latestApplicationTable.tableColumns.financialProduct', { returnObjects: true }),
        title: 'Financial Product',
        dataIndex: ['financeProduct', 'displayName'],
        key: 'financialProduct',
    },
    {
        title: () => statusHeader(t, onStatusIconClicked),
        dataIndex: ['financingStage', 'status'],
        key: 'status',
        render: value => renderStatusTag(value, ApplicationStage.Financing, t),
    },
    {
        ...t('dashboard:latestApplicationTable.tableColumns.lastActivity', { returnObjects: true }),
        dataIndex: ['versioning', 'updatedAt'],
        key: 'appDate',
        render: value => renderDate(value, t),
    },
];

const hasFinancing = (module: ModuleWithScenariosFragment) => {
    switch (module.__typename) {
        case 'StandardApplicationModule':
        case 'ConfiguratorModule':
        case 'FinderApplicationPublicModule':
        case 'FinderApplicationPrivateModule':
            return hasFinancingScenario(module.scenarios);

        default:
            return false;
    }
};

const useLatestApplicationTableData = (onStatusIconClicked: () => void) => {
    const { t } = useTranslation(['dashboard', 'applicationList']);
    const cols = useMemo(() => columns(t, onStatusIconClicked), [onStatusIconClicked, t]);

    const company = useCompany(true);
    const { dealerIds } = useMultipleDealerIds();

    const { data: moduleList } = useGetModulesWithScenariosQuery({
        fetchPolicy: 'cache-and-network',
        variables: { filter: { moduleRole: ModuleRole.Application, companyId: company.id } },
    });

    const hasModulesWithFinancing = useMemo(
        () => moduleList && moduleList.modules?.items?.some(hasFinancing),
        [moduleList]
    );

    const variables = useMemo(
        () => ({
            pagination: {
                offset: 0,
                limit: 5,
            },
            // default sorting
            sort: {
                field: ApplicationSortingField.ApplicationDate,
                order: SortingOrder.Desc,
            },

            filter: { stage: ApplicationStage.Financing, companyIds: company?.id ? [company.id] : null, dealerIds },
        }),
        [company?.id, dealerIds]
    );

    const {
        data,
        loading: isLoading,
        error,
    } = useListApplicationsQuery({
        nextFetchPolicy: 'cache-and-network',
        variables,
        skip: !hasModulesWithFinancing,
    });

    const applicationListData: ApplicationListDataFragment[] = useMemo(
        () => (data?.list?.items || []).map(item => ({ ...item, key: item.id })),
        [data]
    );

    return useMemo(
        () => ({
            isLoading,
            error: (hasModulesWithFinancing !== undefined && !hasModulesWithFinancing) || error,
            columns: cols,
            data: applicationListData,
        }),
        [isLoading, error, cols, applicationListData, hasModulesWithFinancing]
    );
};
export default useLatestApplicationTableData;
