import type { InputRef } from 'antd';
import { Table } from 'antd';
import type { FilterDropdownProps, ColumnFilterItem } from 'antd/lib/table/interface';
import { pick, sortBy } from 'lodash/fp';
import { useRef, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import type { ApplicationListDataFragment } from '../../../api/fragments/ApplicationListData';
import type { StockInventorySpecsFragment } from '../../../api/fragments/StockInventorySpecs';
import { useGetMobilityApplicationsListFiltersQuery } from '../../../api/queries/getMobilityApplicationsListFilters';
import { useListApplicationsQuery } from '../../../api/queries/listApplications';
import { ApplicationKind, ApplicationStatus, ApplicationStage } from '../../../api/types';
import FilterBox from '../../../components/FilterBox';
import PaginatedTableWithContext from '../../../components/PaginatedTableWithContext';
import RangeDateFilterBox, { objectToRangeDateValue } from '../../../components/RangeDateFilterBox';
import SearchBox from '../../../components/SearchBox';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import { useMultipleDealerIds } from '../../../components/contexts/DealerContextManager';
import { useThemeComponents } from '../../../themes/hooks';
import renderFilterIcon from '../../../utilities/renderFilterIcon';
import renderSearchIcon from '../../../utilities/renderSearchIcon';
import useFormatDate from '../../../utilities/useFormatDate';
import useSortAndFilterCache, { SortAndFilterCacheKey } from '../../../utilities/useSortAndFilterCache';
import useTranslatedString from '../../../utilities/useTranslatedString';
import { renderStatusTag } from '../../shared/ApplicationList';
import useSortOrder from '../../shared/ApplicationList/useSortOrder';
import type { PaginatedApplicationTable } from '../MobilityBookingListPage/MobilityBookingList';
import { getSortField } from '../MobilityBookingListPage/MobilityBookingList';
import type { BookingListState } from '../MobilityBookingListPage/useListReducer';
import useListReducer, { defaultBookingListSort } from '../MobilityBookingListPage/useListReducer';

type MobilityDetailsSectionProps = { stock: StockInventorySpecsFragment; vehicleId: string };

const MobilityDetailsSection = ({ stock, vehicleId }: MobilityDetailsSectionProps) => {
    const { t } = useTranslation(['inventoryDetails', 'applicationList', 'common']);
    const { Card } = useThemeComponents();

    const translate = useTranslatedString();

    const inputRef = useRef<InputRef>(null);
    const navigate = useNavigate();
    const [currentCache, setCache] = useSortAndFilterCache<BookingListState>(SortAndFilterCacheKey.MobilityBooking, {
        companyId: '',
        page: 1,
        pageSize: 10,
        filter: {
            status: [ApplicationStatus.NewMobility],
        },
        sort: defaultBookingListSort,
    });
    const [state, dispatch] = useListReducer(currentCache);
    const { page, pageSize, sort, filter } = state;

    const formatDate = useFormatDate();
    const company = useCompany(true);
    const { dealerIds } = useMultipleDealerIds();

    const { data } = useListApplicationsQuery({
        nextFetchPolicy: 'cache-and-network',
        variables: {
            pagination: { offset: (page - 1) * pageSize, limit: pageSize },
            sort: pick(['field', 'order'], sort),
            filter: {
                ...filter,
                kind: ApplicationKind.Mobility,
                dealerIds,
                inventoryStockId: stock.id,
                vehicle: vehicleId,
                stage: ApplicationStage.Mobility,
            },
        },
    });

    const dataSource = useMemo(
        () =>
            ((data?.list?.items as ApplicationListDataFragment[]) || []).map(item => ({
                ...item,
                key: item.id,
            })),
        [data]
    );
    const total = data?.list?.count || 0;

    const { data: appFilterList } = useGetMobilityApplicationsListFiltersQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            filter: {
                kind: ApplicationKind.Mobility,
                dealerIds,
                stage: ApplicationStage.Mobility,
            },
        },
    });

    const [statusList, locationFilter] = useMemo(() => {
        const filterList = appFilterList?.getMobilityApplicationsListFilters;
        if (!filterList) {
            return [[], []];
        }
        const statusList = sortBy(
            'text',
            filterList.applicationStatus.map(status => ({
                text: t(`applicationList:status.${status}`),
                value: status,
            }))
        );

        const locationsListOptions = sortBy(
            'text',
            filterList.mobilityLocations.map(location => {
                if (location.__typename === 'MobilityBookingLocationHome') {
                    return { text: t('common:homeDelivery'), value: 'homeDelivery' };
                }

                return { text: location.name, value: location.name };
            })
        );

        return [statusList, locationsListOptions];
    }, [appFilterList, t]);

    const showSearchDropDown = useCallback(
        (props: FilterDropdownProps, dataIndex: string) =>
            SearchBox({
                filterDropDownProps: props,
                dataIndex,
                onRef: node => {
                    inputRef.current = node;
                },
            }),
        [inputRef]
    );

    const showFilterDropDown = useCallback(
        (props: FilterDropdownProps, filters: ColumnFilterItem[]) =>
            FilterBox({
                filterDropDownProps: props,
                filters,
            }),
        []
    );

    const showRangeFilterDropDown = useCallback(
        (props: FilterDropdownProps) =>
            RangeDateFilterBox({
                filterDropDownProps: props,
                picker: 'date',
            }),
        []
    );

    const onChange = useCallback<PaginatedApplicationTable['onChange']>(
        (pagination, filters, sorter, extra) => {
            switch (extra.action) {
                case 'sort': {
                    return dispatch({
                        type: 'setSort',
                        sortBy: getSortField(sorter),
                    });
                }

                case 'filter': {
                    return dispatch({
                        type: 'setFilter',
                        filteringRule: {
                            locations: !filters.location ? undefined : (filters.location as string[]),
                            customer: !filters.applicant ? undefined : (filters.applicant[0] as string),
                            status: !filters.status ? undefined : (filters.status as ApplicationStatus[]),
                            vin: !filters.vin ? undefined : (filters.vin[0] as string),
                            startDateRange: !filters.startDate
                                ? undefined
                                : {
                                      start: new Date(filters.startDate[0] as string),
                                      end: new Date(filters.startDate[1] as string),
                                  },
                            endDateRange: !filters.endDate
                                ? undefined
                                : {
                                      start: new Date(filters.endDate[0] as string),
                                      end: new Date(filters.endDate[1] as string),
                                  },
                            createdDateRange: !filters.bookingSubmitted
                                ? undefined
                                : {
                                      start: new Date(filters.bookingSubmitted[0] as string),
                                      end: new Date(filters.bookingSubmitted[1] as string),
                                  },
                        },
                    });
                }

                default:
                    return undefined;
            }
        },
        [dispatch]
    );

    const onRow = useCallback<PaginatedApplicationTable['onRow']>(
        record => ({
            onClick: () => {
                setCache(state);
                navigate(`/admin/mobilitybookings/${record.versioning.suiteId}`);
            },
        }),
        [navigate, setCache, state]
    );

    const {
        appDateSort,
        customerSort,
        vehicleNameSort,
        bookingStartDateSort,
        bookingEndDateSort,
        bookingLocationSort,
        totalAmountPaidSort,
        applicationStatusSort,
    } = useSortOrder(sort);

    return (
        <Card title={t('inventoryDetails:sections.mobilityBookings')}>
            <PaginatedTableWithContext
                company={company}
                dataSource={dataSource}
                dispatch={dispatch}
                onChange={onChange}
                onRow={onRow}
                rowKey="id"
                scroll={{ x: true }}
                state={state}
                tableName={t('inventoryDetails:sections.mobilityBookings')}
                total={total}
            >
                <Table.Column
                    key="bookingSubmitted"
                    dataIndex={['versioning', 'createdAt']}
                    filterDropdown={props => showRangeFilterDropDown(props)}
                    filteredValue={objectToRangeDateValue(filter.createdDateRange)}
                    render={date =>
                        t('common:formats.dateTime', {
                            date: new Date(date),
                        })
                    }
                    sortOrder={appDateSort}
                    title={t('applicationList:columns.bookingSubmitted')}
                    sorter
                />
                <Table.Column
                    key="startDate"
                    dataIndex={['mobilityBookingDetails', 'period', 'start']}
                    filterDropdown={props => showRangeFilterDropDown(props)}
                    filteredValue={objectToRangeDateValue(filter.startDateRange)}
                    render={(value, record: ApplicationListDataFragment) =>
                        formatDate({
                            date: value,
                        })
                    }
                    sortOrder={bookingStartDateSort}
                    title={t('applicationList:columns.startDateTime')}
                    sorter
                />
                <Table.Column
                    key="endDate"
                    dataIndex={['mobilityBookingDetails', 'period', 'end']}
                    filterDropdown={props => showRangeFilterDropDown(props)}
                    filteredValue={objectToRangeDateValue(filter.endDateRange)}
                    render={(value, record: ApplicationListDataFragment) =>
                        formatDate({
                            date: value,
                        })
                    }
                    sortOrder={bookingEndDateSort}
                    title={t('applicationList:columns.endDateTime')}
                    sorter
                />
                <Table.Column
                    key="applicant"
                    dataIndex={['applicant', 'fullName']}
                    filterDropdown={props => showSearchDropDown(props, 'applicant')}
                    filterIcon={renderSearchIcon}
                    filteredValue={filter.customer ? [filter.customer] : undefined}
                    sortOrder={customerSort}
                    title={t('applicationList:columns.customer')}
                    sorter
                />
                <Table.Column
                    key="totalAmountPaid"
                    dataIndex={['deposit', 'amount']}
                    sortOrder={totalAmountPaidSort}
                    title={t('applicationList:columns.totalAmountPaid')}
                    sorter
                />
                <Table.Column
                    key="vehicle"
                    dataIndex={['vehicle', 'name']}
                    render={value => translate(value)}
                    sortOrder={vehicleNameSort}
                    title={t('applicationList:columns.vehicle')}
                    sorter
                />
                <Table.Column
                    dataIndex={['vin']}
                    filterDropdown={props => showSearchDropDown(props, 'vin')}
                    filterIcon={renderSearchIcon}
                    filteredValue={filter.vin ? [filter.vin] : undefined}
                    title={t('applicationList:columns.vin')}
                />
                <Table.Column
                    key="location"
                    dataIndex={['mobilityBookingDetails', 'location', 'name']}
                    filterDropdown={props => showFilterDropDown(props, locationFilter)}
                    filterIcon={renderFilterIcon}
                    filteredValue={filter.locations}
                    sortOrder={bookingLocationSort}
                    title={t('applicationList:columns.location')}
                    sorter
                />
                <Table.Column
                    key="status"
                    dataIndex={['mobilityStage', 'status']}
                    filterDropdown={props => showFilterDropDown(props, statusList)}
                    filterIcon={renderFilterIcon}
                    filteredValue={filter.status}
                    onFilter={(value: string, record: ApplicationListDataFragment) =>
                        record.mobilityStage?.status.indexOf(value) === 0
                    }
                    render={value => renderStatusTag(value, ApplicationStage.Mobility, t)}
                    sortOrder={applicationStatusSort}
                    title={t('applicationList:columns.status')}
                    sorter
                />
            </PaginatedTableWithContext>
        </Card>
    );
};

export default MobilityDetailsSection;
