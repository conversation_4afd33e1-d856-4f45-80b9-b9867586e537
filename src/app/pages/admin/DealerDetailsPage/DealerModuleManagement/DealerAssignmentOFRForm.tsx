/* eslint-disable max-len */
import { Col, message } from 'antd';
import { Formik } from 'formik';
import { omit } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import * as permissionKind from '../../../../../shared/permissions';
import type { DealerWithPermissionsFragmentFragment } from '../../../../api/fragments/DealerWithPermissionsFragment';
import type { OfrModuleInDealerSpecsFragment } from '../../../../api/fragments/OFRModuleInDealerSpecs';
import { useUpdateApplicationDealershipAssignmentsByDealerMutation } from '../../../../api/mutations/updateApplicationDealershipAssignmentsByDealer';
import { useUpdateOfrModuleEmailContentMutation } from '../../../../api/mutations/updateOFRModuleEmailContent';
import type { DealerOfrEquityOverridesInput } from '../../../../api/types';
import { EmailContentUpdateType } from '../../../../api/types';
import Form from '../../../../components/fields/Form';
import InputNumberField from '../../../../components/fields/InputNumberField';
import SelectField from '../../../../components/fields/SelectField';
import CollapsibleWrapper, { Panel } from '../../../../components/wrappers/CollapsibleWrapper';
import hasPermissions from '../../../../utilities/hasPermissions';
import useHandleError from '../../../../utilities/useHandleError';
import OFRModuleEmailSection from '../../ModuleDetailsPage/OFRModulePage/OFRModuleEmailSection';
import OFRRVTable from '../../ModuleDetailsPage/OFRModulePage/RVTable';

export type DealerAssignmentOFRFormProps = {
    module: OfrModuleInDealerSpecsFragment;
    dealer: DealerWithPermissionsFragmentFragment;
};

type FormValues = {
    emailContents: OfrModuleInDealerSpecsFragment['emailContents'];
    equity: DealerOfrEquityOverridesInput;
    rvSettings: OfrModuleInDealerSpecsFragment['rvSettings'];
};

const emptyDefaultValue: (currency: string, dealerId: string) => DealerOfrEquityOverridesInput = (
    currency: string,
    dealerId: string
) => ({
    value: 0,
    unit: currency,
    dealerId,
});

const DealerAssignmentOFRForm = ({ module, dealer }: DealerAssignmentOFRFormProps) => {
    if (module.__typename !== 'OFRModule') {
        return null;
    }
    const { t } = useTranslation(['dealershipManagement', 'moduleDetails', 'ofrModuleDetails']);
    const [mutation] = useUpdateApplicationDealershipAssignmentsByDealerMutation();
    const [emailContentMutation] = useUpdateOfrModuleEmailContentMutation();
    const hasUpdatePermission = hasPermissions(dealer.permissions, [permissionKind.updateDealer]);
    const { emailContents } = module;
    // initialize emailContents, equity, rvtable for the dealer
    const initialValues: FormValues = useMemo(
        () => ({
            emailContents,
            equity:
                module.equity.overrides?.find(equity => equity.dealerId === dealer.id) ||
                emptyDefaultValue(module.company.currency, dealer.id),
            rvSettings: module.rvSettings,
        }),
        [dealer.id, emailContents, module.company.currency, module.equity?.overrides]
    );

    const onSubmit = useHandleError<FormValues>(
        async values => {
            const { emailContents, equity } = values;

            // submitting message
            message.loading({
                content: t('dealershipManagement:messages.updateSubmitting'),
                key: 'primary',
                duration: 0,
            });

            const variables = {
                moduleId: module.id,
                equity,
                dealerRVTable: {
                    dealerId: dealer.id,
                    table:
                        values.rvSettings.overrides?.find(override => override.dealerId === dealer.id)?.table ||
                        values.rvSettings.table,
                },
            };

            await mutation({ variables });

            await emailContentMutation({
                variables: {
                    moduleId: module.id,
                    settings: {
                        customer: {
                            newOffer: omit('introImage', emailContents.customer.newOffer),
                        },
                        salesConsultant: {
                            equityNotification: omit('introImage', emailContents.salesConsultant.equityNotification),
                        },
                    },
                    emailContentUpdateType: EmailContentUpdateType.Dealer,
                    dealerId: dealer.id,
                },
            });
            message.destroy('primary');

            // show successful message
            message.success({
                content: t('dealershipManagement:messages.updateSuccessful'),
                key: 'primary',
            });
        },
        [t, dealer.id, module.id, mutation, emailContentMutation]
    );

    const formId = `dealerForm-${module.id}`;

    const prefixDropdown = [
        { label: module.company.currency, value: module.company.currency },
        { label: '%', value: '%' },
    ];

    return (
        <Formik<FormValues> initialValues={initialValues} onSubmit={onSubmit} enableReinitialize>
            {({ handleSubmit, values }) => (
                <Form id={formId} name={formId} onSubmitCapture={handleSubmit}>
                    <CollapsibleWrapper defaultActiveKey={['configuration']}>
                        <Panel
                            key="configuration"
                            className="added-bottom-padding"
                            header={t('moduleDetails:sections.configuration')}
                        >
                            <Col lg={8} xs={24}>
                                <InputNumberField
                                    name="equity.value"
                                    {...t('moduleDetails:fields.equity', { returnObjects: true })}
                                    addonAfter={
                                        <SelectField
                                            defaultValue={prefixDropdown[0].value}
                                            itemProps={{ noStyle: true }}
                                            name="equity.unit"
                                            options={prefixDropdown}
                                        />
                                    }
                                    disabled={!hasUpdatePermission}
                                    required
                                />
                            </Col>
                            <Col>
                                <OFRRVTable dealerId={dealer.id} disabled={!hasUpdatePermission} moduleId={module.id} />
                            </Col>
                        </Panel>
                    </CollapsibleWrapper>
                    <OFRModuleEmailSection
                        companyId={module.company.id}
                        dealer={dealer}
                        emailContents={emailContents}
                        targetDealerId={dealer.id}
                        defaultValueDisabled
                    />
                </Form>
            )}
        </Formik>
    );
};

export default DealerAssignmentOFRForm;
