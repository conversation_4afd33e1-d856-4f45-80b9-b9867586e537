import { DeleteOutlined } from '@ant-design/icons';
import type { TableProps } from 'antd';
import { Button, Table, Typography } from 'antd';
import dayjs from 'dayjs';
import type { <PERSON><PERSON>vent<PERSON>andler } from 'react';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router';
import type { StockInventorySpecsFragment } from '../../../api/fragments/StockInventorySpecs';
import type {
    DeleteStockInventoryMutation,
    DeleteStockInventoryMutationVariables,
} from '../../../api/mutations/deleteStockInventory';
import { DeleteStockInventoryDocument } from '../../../api/mutations/deleteStockInventory';
import { GetInventoryDocument } from '../../../api/queries/getInventory';
import type { MobilityStockInventory, ReservationStockStatus } from '../../../api/types';
import { ApplicationStage } from '../../../api/types';
import CollapsibleWrapper, { Panel } from '../../../components/wrappers/CollapsibleWrapper';
import useConsolePermissions from '../../../layouts/useConsolePermissions';
import { useThemeComponents } from '../../../themes/hooks';
import type { PermissionValues } from '../../../utilities/hasPermissions';
import useFormats from '../../../utilities/useFormats';
import useSystemOptions from '../../../utilities/useSystemOptions';
import { getAdminReferencePaths } from '../../shared/ApplicationDetailsPage/generic/shared';
import {
    ReferenceApplicationList,
    ReferenceApplicationProvider,
} from '../../shared/ApplicationDetailsPage/standard/ApplicationTab/ReferenceApplicationField';
import useDeletionConfirm from '../VariantConfiguratorDetailsPage/useDeletionConfirm';
import { useInventory } from './InventoryContext';

const InventoryStockSection = ({ permissions }: { permissions: Pick<PermissionValues, 'hasDeletePermission'> }) => {
    const { t } = useTranslation(['inventoryDetails']);
    const { GenericTable } = useThemeComponents();

    const { stockStatuses } = useSystemOptions();

    const inventory = useInventory();
    const navigate = useNavigate();
    const location = useLocation();

    const { formatAmount } = useFormats(
        inventory.module.company?.currency || '',
        inventory.module.company?.roundings?.amount?.decimals || 0,
        inventory.module.company?.roundings?.percentage?.decimals || 0
    );

    const { hasDeletePermission } = permissions;

    const deleteConfirmation = useDeletionConfirm<DeleteStockInventoryMutation, DeleteStockInventoryMutationVariables>(
        'inventoryDetails',
        'confirms.deleteStock',
        'messages.deleteStock'
    );

    const stockStatusesLabel = useMemo(
        () =>
            stockStatuses.reduce(
                (accumulator, current) => ({
                    ...accumulator,
                    [current.value]: current.label,
                }),
                {} as Record<string, ReservationStockStatus>
            ),
        [stockStatuses]
    );

    const deleteStock = useCallback(
        (stockId: string) => {
            deleteConfirmation(
                {
                    mutation: DeleteStockInventoryDocument,
                    variables: {
                        id: stockId,
                        inventoryId: inventory.id,
                    },
                    refetchQueries: [GetInventoryDocument],
                },
                () => {}
            );
        },
        [deleteConfirmation, inventory.id]
    );

    const handleClickDelete = useCallback(
        (id: string): MouseEventHandler<HTMLElement> =>
            ev => {
                // because there is row on click
                // so should stop propagation event to parent element
                ev.stopPropagation();
                deleteStock(id);
            },
        [deleteStock]
    );

    const { hasLeadsAndContacts } = useConsolePermissions();

    return (
        <ReferenceApplicationProvider
            referencePaths={{
                stages: getAdminReferencePaths(hasLeadsAndContacts),
                references: {},
            }}
        >
            <CollapsibleWrapper defaultActiveKey="inventoryStocksDetails">
                <Panel
                    key="inventoryStocksDetails"
                    header={
                        <Typography.Title level={5}>
                            {t('inventoryDetails:sections.stocks', {
                                unused: inventory.availableStock,
                                reserved: inventory.reservedStock,
                                total: inventory.stocks.length,
                            })}
                        </Typography.Title>
                    }
                >
                    <GenericTable<(props: TableProps<StockInventorySpecsFragment>) => JSX.Element>
                        dataSource={inventory.stocks}
                        onRow={record => ({
                            onClick: () => {
                                navigate(`${record.id}`, {
                                    state: { currentPage: location.state?.currentPage || 'inventoryList' },
                                });
                            },
                            style: {
                                cursor: 'pointer',
                            },
                        })}
                        rowKey="id"
                        scroll={{ x: true }}
                    >
                        <Table.Column
                            key="no"
                            dataIndex="index"
                            render={(value, row, rowIndex) => rowIndex + 1}
                            title={t('inventoryDetails:stocks.no.label')}
                        />
                        <Table.Column
                            key="identifier"
                            dataIndex="identifier"
                            title={t('inventoryDetails:stocks.identifier.label')}
                        />
                        <Table.Column key="vin" dataIndex="vin" title={t('inventoryDetails:stocks.vin.label')} />
                        <Table.Column
                            key="price"
                            dataIndex="price"
                            render={(price: number, row: MobilityStockInventory) =>
                                `${inventory.module.company?.currency} ${formatAmount(
                                    row?.__typename === 'MobilityStockInventory' && price ? price : inventory.price
                                )}`
                            }
                            title={t('inventoryDetails:stocks.price.label')}
                        />
                        <Table.Column
                            key="reservationStatus"
                            dataIndex="reservationStatus"
                            render={(value: ReservationStockStatus) => stockStatusesLabel[value]}
                            title={t('inventoryDetails:stocks.status.label')}
                        />
                        <Table.Column
                            key="period"
                            dataIndex="period"
                            render={value =>
                                value &&
                                `${dayjs(value.start).format('DD MMM YYYY')} - ${dayjs(value.end).format('DD MMM YYYY')}`
                            }
                            title={t('inventoryDetails:fields.period.label')}
                        />
                        {inventory.__typename === 'ConfiguratorInventory' && (
                            <Table.Column<StockInventorySpecsFragment>
                                key="transaction"
                                dataIndex={['application', 'id']}
                                render={(value, row, rowIndex) =>
                                    row.__typename === 'ConfiguratorStockInventory' && row.application ? (
                                        <div>
                                            <ReferenceApplicationList
                                                application={row.application}
                                                style={{ paddingTop: 0 }}
                                            />
                                        </div>
                                    ) : (
                                        ''
                                    )
                                }
                                title={t('inventoryDetails:stocks.transaction.label')}
                            />
                        )}
                        {hasDeletePermission ? (
                            <Table.Column
                                key="delete"
                                dataIndex="id"
                                render={(value, row, rowIndex) => (
                                    <Button
                                        icon={<DeleteOutlined />}
                                        onClick={handleClickDelete(value)}
                                        type="link"
                                        danger
                                    />
                                )}
                            />
                        ) : null}
                    </GenericTable>
                </Panel>
            </CollapsibleWrapper>
        </ReferenceApplicationProvider>
    );
};

export default InventoryStockSection;
