/* eslint-disable max-len */
import { Space } from 'antd';
import * as permissionKind from '../../../../../shared/permissions';
import type { LeadDataFragment } from '../../../../api/fragments/LeadData';
import { ApplicationStage, CustomerKind, LeadStatus } from '../../../../api/types';
import CapActionAlert from '../../../../components/cap/CapActionAlert';
import { useAccount } from '../../../../components/contexts/AccountContextManager';
import { useLeadDetailsExtraContext } from '../../../../components/contexts/LeadDetailsExtraContext';
import hasPermissions from '../../../../utilities/hasPermissions';
import { hasTradeInKYC } from '../../../shared/ApplicationDetailsPage/standard/ApplicationTab';
import AdditionalInformationPanel from '../../../shared/ApplicationDetailsPage/standard/ApplicationTab/AdditionalInformationPanel';
import RelatedApplicationPanel from '../../../shared/ApplicationDetailsPage/standard/ApplicationTab/RelatedApplicationPanel';
import ApplicantPanel from './ApplicantPanel';
import ConsentsPanel from './ConsentsPanel';
import MainPanel from './MainPanel';
import RetainFinancingPanel from './RetainFinancingPanel';
import RetainVehiclePanel from './RetainVehiclePanel';
import TradeInVehiclePanel from './TradeInVehiclePanel';
import VehiclePanel from './VehiclePanel';

type LeadTabProps = {
    lead: LeadDataFragment;
    isMask: boolean;
};

const LeadTab = ({ lead, isMask }: LeadTabProps) => {
    const { forCI, refetch } = useLeadDetailsExtraContext();

    const { permissions: accountPermissions } = useAccount();
    const hasViewCustomerPermission = hasPermissions(accountPermissions, [permissionKind.viewCustomers]);

    return (
        <Space direction="vertical" size={forCI ? 48 : 16} style={{ display: 'flex' }}>
            <CapActionAlert forCI={forCI} lead={lead} refetch={refetch} />
            <MainPanel lead={lead} />
            <RelatedApplicationPanel applications={lead.applications} forCI={forCI} stage={ApplicationStage.Lead} />
            {hasViewCustomerPermission && (
                <ApplicantPanel
                    capValues={lead.capValues}
                    customer={lead.customer}
                    customerKind={CustomerKind.Local}
                    disabled={lead?.status === LeadStatus.Merged || lead?.status === LeadStatus.Merging}
                    forCI={forCI}
                    isMask={isMask}
                    lead={lead}
                    defaultExpanded
                />
            )}
            <ConsentsPanel lead={lead} />
            {hasTradeInKYC(lead.customerKYC) && <TradeInVehiclePanel forCI={forCI} lead={lead} />}
            {lead.__typename === 'EventLead' && (
                <AdditionalInformationPanel customizedFields={lead.customizedFields} forCi={forCI} />
            )}
            {lead.vehicle && <VehiclePanel lead={lead} />}
            <RetainVehiclePanel lead={lead} />
            <RetainFinancingPanel lead={lead} />
        </Space>
    );
};

export default LeadTab;
