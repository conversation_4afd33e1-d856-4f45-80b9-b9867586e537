import { Col } from 'antd';
import { useTranslation } from 'react-i18next';
import { type ListDealerOption } from '../../../../api/types';
// eslint-disable-next-line max-len
import DealershipTranslatedTextAreaField from '../../../../components/fields/DealershipFields/DealershipTranslatedTextArea';
// eslint-disable-next-line max-len
import DealershipTranslationTextField from '../../../../components/fields/DealershipFields/DealershipTranslationTextField';
import SingleUploadField from '../../../../components/fields/SingleUploadField';
import { allowedExtensions } from '../../../../utilities/extensions';

type EventApplicationEmailContentsItemProps = {
    targetDealerIds: string[];
    defaultValueDisabled: boolean;
    disabled?: boolean;
    handleNames: (name: string) => string;
    targetDealerId?: string;
    isEventLevelContents?: boolean;
    dealers?: ListDealerOption[];
};

const EventApplicationEmailContentsItem = ({
    targetDealerIds,
    defaultValueDisabled,
    disabled,
    handleNames,
    targetDealerId,
    isEventLevelContents,
    dealers,
}: EventApplicationEmailContentsItemProps) => {
    const { t } = useTranslation('eventApplicationModuleDetails');

    console.log('handleNames(subject): ', handleNames('subject'));
    console.log('handleNames(introTitle): ', handleNames('introTitle'));
    console.log('handleNames(contentText): ', handleNames('contentText'));

    return (
        <>
            <Col lg={8} xs={24}>
                <DealershipTranslationTextField
                    {...t('eventApplicationModuleDetails:fields.submitOrder.subject', {
                        returnObjects: true,
                    })}
                    dealerIds={targetDealerIds}
                    defaultValueDisabled={defaultValueDisabled}
                    disabled={disabled}
                    name={handleNames('subject')}
                    required={isEventLevelContents}
                />
            </Col>
            <Col lg={8} xs={24}>
                <DealershipTranslationTextField
                    {...t('eventApplicationModuleDetails:fields.submitOrder.introTitle', {
                        returnObjects: true,
                    })}
                    dealerIds={targetDealerIds}
                    defaultValueDisabled={defaultValueDisabled}
                    disabled={disabled}
                    name={handleNames('introTitle')}
                    required={!isEventLevelContents}
                />
            </Col>
            <Col lg={8} xs={24}>
                <DealershipTranslatedTextAreaField
                    {...t('eventApplicationModuleDetails:fields.submitOrder.contentText', {
                        returnObjects: true,
                    })}
                    dealers={dealers}
                    defaultValueDisabled={defaultValueDisabled}
                    disabled={disabled}
                    name={handleNames('contentText')}
                    required={isEventLevelContents}
                    targetDealerId={targetDealerId}
                    withContentRefinement
                />
            </Col>
            <Col lg={8} xs={24}>
                <SingleUploadField
                    disabled={disabled}
                    extensions={allowedExtensions.image}
                    {...t('eventApplicationModuleDetails:fields.submitOrder.introImage', {
                        returnObjects: true,
                    })}
                    name={handleNames('introImage')}
                />
            </Col>
        </>
    );
};

export default EventApplicationEmailContentsItem;
