import { Col, Row } from 'antd';
import { useTranslation } from 'react-i18next';
import InputField from '../../../../components/fields/InputField';
import { useThemeComponents } from '../../../../themes/hooks';

const PorscheIdModuleMainDetailsForm = () => {
    const { t } = useTranslation(['porscheIdModuleDetails']);
    const { Card } = useThemeComponents();

    return (
        <Card>
            <Row gutter={16}>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('porscheIdModuleDetails:fields.displayName', { returnObjects: true })}
                        name="displayName"
                        required
                    />
                </Col>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('porscheIdModuleDetails:fields.apiKey', { returnObjects: true })}
                        name="porscheIdSetting.apiKey"
                        required
                    />
                </Col>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('porscheIdModuleDetails:fields.identityProvider', { returnObjects: true })}
                        name="porscheIdSetting.identityProvider"
                        required
                    />
                </Col>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('porscheIdModuleDetails:fields.audience', { returnObjects: true })}
                        name="porscheIdSetting.audience"
                        required
                    />
                </Col>
                <Col md={8} xs={24}>
                    <InputField
                        {...t('porscheIdModuleDetails:fields.userDataBaseUrl', { returnObjects: true })}
                        name="porscheIdSetting.userDataBaseUrl"
                        required
                    />
                </Col>
            </Row>
        </Card>
    );
};

export default PorscheIdModuleMainDetailsForm;
