/* eslint-disable max-len */
import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import { Formik } from 'formik';
import { pick } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import type { PorscheIdModuleSpecsFragment } from '../../../../api/fragments/PorscheIdModuleSpecs';
import {
    UpdatePorscheIdModuleDocument,
    type UpdatePorscheIdModuleMutation,
    type UpdatePorscheIdModuleMutationVariables,
} from '../../../../api/mutations/updatePorscheIdModule';
import type { PorscheIdModuleSettings } from '../../../../api/types';
import Form from '../../../../components/fields/Form';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import PorscheIdModuleMainDetailsForm from './PorscheIdModuleDetailsForm';

export type FormValues = PorscheIdModuleSettings;

const PorscheIdModuleMainDetails = ({ module }: { module: PorscheIdModuleSpecsFragment }) => {
    const { t } = useTranslation(['porscheIdModuleDetails']);

    const initialValues = useMemo(
        (): PorscheIdModuleSettings => ({
            ...pick(['displayName', 'porscheIdSetting'], module),
        }),
        [module]
    );

    const apolloClient = useApolloClient();

    const validate = useValidator(
        validators.compose(
            validators.requiredString('displayName'),
            validators.requiredString('porscheIdSetting.apiKey'),
            validators.requiredString('porscheIdSetting.identityProvider'),
            validators.requiredString('porscheIdSetting.audience'),
            validators.requiredString('porscheIdSetting.userDataBaseUrl')
        )
    );

    const onSubmit = useHandleError<FormValues>(
        async values => {
            // submitting message
            message.loading({
                content: t('porscheIdModuleDetails:messages.updateMainDetailsSubmitting'),
                key: 'primary',
                duration: 0,
            });

            // submit creation
            await apolloClient
                .mutate<UpdatePorscheIdModuleMutation, UpdatePorscheIdModuleMutationVariables>({
                    mutation: UpdatePorscheIdModuleDocument,
                    variables: {
                        moduleId: module.id,
                        settings: values,
                    },
                })
                .finally(() => {
                    message.destroy('primary');
                });

            // inform about success
            message.success({
                content: t('porscheIdModuleDetails:messages.updateMainDetailsSuccessful'),
                key: 'primary',
            });
        },
        [apolloClient, module, t]
    );

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ handleSubmit }) => (
                <Form id="updateMainDetails" name="updateMainDetails" onSubmitCapture={handleSubmit}>
                    <PorscheIdModuleMainDetailsForm />
                </Form>
            )}
        </Formik>
    );
};

export default PorscheIdModuleMainDetails;
