import { useApolloClient } from '@apollo/client';
import type { UploadProps } from 'antd';
import { Row, Typography, Upload, message } from 'antd';
import { isNil } from 'lodash';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import type {
    GetImportedOfrrvTableFromExcelQuery,
    GetImportedOfrrvTableFromExcelQueryVariables,
} from '../../../../../../api/queries/getImportedOFRRVTableFromExcel';
import { GetImportedOfrrvTableFromExcelDocument } from '../../../../../../api/queries/getImportedOFRRVTableFromExcel';
import { CompanyTheme, OfrRvTableCell } from '../../../../../../api/types';
import ThemeUpload from '../../../../../../components/ThemeUpload';
import { ErrorContent } from '../../../../../../components/importExport';
import CancelButton from '../../../../../../components/modal/CancelButton';
import OkButton from '../../../../../../components/modal/OkButton';
import { useThemeComponents } from '../../../../../../themes/hooks';
import { allowedExtensions, validateExtensions } from '../../../../../../utilities/extensions';

export type ImportRVTableModalProps = {
    title: string;
    open: boolean;
    onImport: (data: OfrRvTableCell[]) => void;
    onClose: () => void;
};

const ImportRVTableModal = ({ title, open, onImport, onClose }: ImportRVTableModalProps) => {
    const { t } = useTranslation(['ofrModuleDetails', 'importExportExcel']);
    const apolloClient = useApolloClient();
    const [errors, setErrors] = useState<string>(null);
    const { theme, Modal } = useThemeComponents();
    const accept = useMemo(() => allowedExtensions.excel.join(','), []);

    const handleOnClose = useCallback(() => {
        onClose();
        setErrors(null);
    }, [onClose]);

    const handleOnChange = useCallback(
        async ({ file }) => {
            if (file instanceof File) {
                const { data } = await apolloClient.query<
                    GetImportedOfrrvTableFromExcelQuery,
                    GetImportedOfrrvTableFromExcelQueryVariables
                >({
                    query: GetImportedOfrrvTableFromExcelDocument,
                    variables: {
                        upload: file,
                    },
                });

                const { table, errors } = data.getImportedOFRRVTableFromExcel;

                // check if there is any error
                if (errors.length) {
                    setErrors(errors.join('\n'));

                    return;
                }

                if (table.length) {
                    onImport(table);
                    onClose();
                }
            }
        },
        [apolloClient, onClose, onImport]
    );

    const beforeUpload = useCallback<NonNullable<UploadProps['beforeUpload']>>(
        file => {
            const reader = new FileReader();
            const promise = new Promise<boolean | string>((resolve, reject) => {
                reader.onload = async event => {
                    const content = event.target.result as string;
                    const validationResult = await validateExtensions(content, file.name);
                    if (validationResult.valid) {
                        return resolve(false);
                    }
                    message.error(t('common:upload.messages.fileError'));

                    return resolve(Upload.LIST_IGNORE);
                };

                reader.onerror = event => resolve(Upload.LIST_IGNORE);
            });

            reader.readAsBinaryString(file);

            return Promise.resolve(promise);
        },
        [t]
    );

    const content = useMemo(() => {
        if (!isNil(errors)) {
            return <ErrorContent errors={errors} />;
        }

        return (
            <Row gutter={[0, 14]}>
                <Typography>{t('importExportExcel:description.message')}</Typography>
                <Typography>{t('importExportExcel:description.note')}</Typography>
            </Row>
        );
    }, [errors, t]);

    const footer = useMemo(() => {
        const cancelButton = (
            <CancelButton key="close" onClick={handleOnClose}>
                {t('ofrModuleDetails:rvTable.actions.close')}
            </CancelButton>
        );

        if (!isNil(errors)) {
            return [cancelButton];
        }

        const okButton = (
            <ThemeUpload
                key="upload"
                accept={accept}
                beforeUpload={beforeUpload}
                onChange={handleOnChange}
                showUploadList={false}
            >
                <OkButton>{t('ofrModuleDetails:rvTable.actions.uploadFile')}</OkButton>
            </ThemeUpload>
        );

        if (theme === CompanyTheme.PorscheV3 || theme === CompanyTheme.Porsche) {
            return [okButton, cancelButton];
        }

        return [cancelButton, okButton];
    }, [accept, beforeUpload, errors, handleOnChange, handleOnClose, theme, t]);

    return (
        <Modal footer={footer} onCancel={handleOnClose} open={open} title={title} width={550}>
            {content}
        </Modal>
    );
};

export default ImportRVTableModal;

export type ImportExcelProps = Pick<ImportRVTableModalProps, 'title' | 'onImport'>;

export const useImportTablesModal = () => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: () => setVisible(false),
        }),
        [setVisible]
    );

    return {
        ...actions,
        render: (props: ImportExcelProps) => <ImportRVTableModal onClose={actions.close} open={visible} {...props} />,
    };
};
