import { useFormikContext } from 'formik';
import { isNil } from 'lodash';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { OfrModuleSetting, OfrRvTableCell } from '../../../../../api/types';
import { useAccountContext } from '../../../../../components/contexts/AccountContextManager';
import { useThemeComponents } from '../../../../../themes/hooks';
import exportOfrRvTable from '../../../../../utilities/export/ofrRVTable';
import EditableTable from '../../../FinanceProductDetailsPage/FinanceProductForm/tables/EditableTable';
import ExportExcel from './Export/ExportExcel';
import ImportExcel from './Import/ImportExcel';
import useRVReferenceData from './useRVReferenceData';
import useYears from './useYears';

const RVTableTitle = styled.div`
    font-weight: bold;
    margin: 0 0 8px 0;
`;

const ActionsContainer = styled.div`
    margin-top: 16px;
`;

export type FormValues = Pick<OfrModuleSetting, 'rvSettings'>;

const OFRRVTable = ({
    dealerId,
    moduleId,
    disabled = false,
}: {
    dealerId?: string;
    moduleId: string;
    disabled: boolean;
}) => {
    const { t } = useTranslation(['ofrModuleDetails']);
    const { values, setFieldValue } = useFormikContext<FormValues>();
    const { notification } = useThemeComponents();
    const { token } = useAccountContext();

    const rvTable = useMemo(() => {
        if (dealerId) {
            const override = values?.rvSettings?.overrides?.find(item => item.dealerId === dealerId);

            return override?.table || values?.rvSettings?.table || [];
        }

        return values?.rvSettings?.table ?? [];
    }, [dealerId, values]);

    const { modelColumns, modelRecords } = useRVReferenceData({
        dealerId,
        rvTable,
        isDisabled: disabled,
    });

    const years = useYears(rvTable);

    const onImport = useCallback(
        async (data: OfrRvTableCell[]) => {
            const currentRecords = modelRecords;
            if (data) {
                const tableDataList = data
                    ?.filter(
                        ({ value, model, year }) =>
                            !isNil(value) &&
                            currentRecords.some(current => `${current.model}` === `${model}`) &&
                            years.includes(year)
                    )
                    .map(({ year, model, value }) => ({
                        year,
                        model: currentRecords.find(current => `${current.model}` === `${model}`).model,
                        value,
                    }));

                if (dealerId) {
                    setFieldValue('rvSettings', {
                        ...(values.rvSettings ?? []),
                        overrides: [
                            ...(values.rvSettings?.overrides?.filter(override => override.dealerId !== dealerId) ?? []),
                            {
                                dealerId: dealerId!,
                                table: data,
                            },
                        ],
                    });
                } else {
                    setFieldValue('rvSettings', {
                        ...(values.rvSettings ?? []),
                        table: data,
                    });
                }
            }
        },
        [setFieldValue, modelRecords]
    );

    const exportExcel = useCallback(async () => {
        try {
            await exportOfrRvTable({
                token,
                moduleId,
                dealerId,
            });
        } catch (error) {
            notification.error(error);
        }
    }, [token, moduleId, dealerId, notification]);

    return (
        <>
            <RVTableTitle>{t('ofrModuleDetails:rvTable.title')}</RVTableTitle>
            <EditableTable columns={modelColumns} records={modelRecords} unitIsPercentage />
            {!disabled && (
                <ActionsContainer>
                    <ImportExcel onImport={onImport} title={t('ofrModuleDetails:rvTable.importModalTitle')} />
                    <ExportExcel onClick={exportExcel} />
                </ActionsContainer>
            )}
        </>
    );
};

export default OFRRVTable;
