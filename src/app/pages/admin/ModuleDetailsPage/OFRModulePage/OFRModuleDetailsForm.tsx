import { Col, Row } from 'antd';
import { useTranslation } from 'react-i18next';
import * as permissionKind from '../../../../../shared/permissions';
import type { OfrModuleSpecsFragment } from '../../../../api/fragments/OFRModuleSpecs';
import type { OfrModuleSetting } from '../../../../api/types';
import DealershipInputNumberField from '../../../../components/fields/DealershipFields/DealerInputNumberField';
import CollapsibleWrapper, { Panel } from '../../../../components/wrappers/CollapsibleWrapper';
import { useThemeComponents } from '../../../../themes/hooks';
import hasPermissions from '../../../../utilities/hasPermissions';
import OFRRVTable from './RVTable';

export type FormValues = OfrModuleSetting;

type OFRModuleProps = {
    module?: OfrModuleSpecsFragment;
};

const OFRModuleDetailsForm = ({ module }: OFRModuleProps) => {
    const { t } = useTranslation(['moduleDetails']);

    const {
        FormFields: { InputField },
    } = useThemeComponents();

    const permissions = {
        hasUpdatePermission: hasPermissions(module.permissions, [permissionKind.updateModule]),
    };

    return (
        <CollapsibleWrapper defaultActiveKey="mainDetails">
            <Panel key="mainDetails" className="added-bottom-padding" header={t('moduleDetails:sections.mainDetails')}>
                <Row gutter={16}>
                    <Col md={8} xs={24}>
                        <InputField
                            {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                            name="displayName"
                            required
                        />
                    </Col>
                    <Col md={8} xs={24}>
                        <DealershipInputNumberField
                            {...t('moduleDetails:fields.equity', { returnObjects: true })}
                            currency={module?.company.currency}
                            name="equity"
                            hasAddOnBefore
                        />
                    </Col>
                </Row>
                <Row gutter={16}>
                    <Col span={24}>
                        <OFRRVTable disabled={!permissions.hasUpdatePermission} moduleId={module.id} />
                    </Col>
                </Row>
            </Panel>
        </CollapsibleWrapper>
    );
};

export default OFRModuleDetailsForm;
