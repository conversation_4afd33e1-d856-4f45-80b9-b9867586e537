import { PTableRow } from '@porsche-design-system/components-react';
import { motionDurationModerate, motionEasingBase } from '@porsche-design-system/components-react/styles';
import { useCallback, useEffect, useState } from 'react';
import styled from 'styled-components';
import type { GetSalesControlBoardQuery } from '../../../../api/queries/getSalesControlBoard';
import { getTotalValueBySalesConsultant } from './ModelInnerCell';
import type { PerformanceTableValueKey } from './Sorter';

export const featureDevStyle = (value: number, index: number) => {
    if ([4, 7, 10, 13, 16].includes(index)) {
        if (value > 0) {
            return 'notification-success';
        }

        if (value < 0) {
            return 'notification-error';
        }

        return 'notification-neutral';
    }

    return 'default';
};

export const CollapsibleHeader = styled(PTableRow).attrs({ 'data-empty': '' })`
    display: table-row;
    cursor: pointer;
    width: 100%;
`;

export const CollapsibleContent = styled.div<{
    isOpen: boolean;
    overflowVisible?: boolean;
}>`
    display: ${({ isOpen }) => (isOpen ? 'inherit' : 'none')};
    max-height: ${({ isOpen }) => (isOpen ? '1000px' : '0px')};
    transition: max-height ${motionDurationModerate} ${motionEasingBase};
    overflow: ${({ overflowVisible }) => (overflowVisible ? 'visible' : 'hidden')};

    .ant-form-item {
        margin-bottom: 0px;
    }
`;

type CollapsibleContentProps = React.PropsWithChildren<{
    item:
        | GetSalesControlBoardQuery['salesControlBoard']['performance']['total'][number]
        | GetSalesControlBoardQuery['salesControlBoard']['performance']['model'][number];
    name: string;
    columns: { key: PerformanceTableValueKey; label: string }[];
    defaultOpen?: boolean;
    type: 'modelName' | 'salesConsultantName';
    isForExportPdf: boolean;
}>;

const PerformanceTableRowCollapsibleContent = ({
    children,
    name,
    item,
    type,
    columns,
    isForExportPdf,
    defaultOpen = false,
}: CollapsibleContentProps) => {
    const [isOpen, setIsOpen] = useState(defaultOpen);

    // fix calendar popup issue with parent overflow hidden
    // toggle overflow between hidden and visible without losing the collapse/expand animation
    const [overflowVisible, setOverflowVisible] = useState(false);

    const handleToggle = useCallback(() => setIsOpen(!isOpen), [isOpen]);

    useEffect(() => {
        let timeout = null;
        if (isOpen) {
            // delay overflow change until animation ends
            timeout = setTimeout(() => setOverflowVisible(true), 400); // transition = "0.4s"
        } else {
            setOverflowVisible(false); // immediately hide overflow on collapse
        }

        return () => {
            if (timeout) {
                // cleanup timeout
                clearTimeout(timeout);
            }
        };
    }, [isOpen]);

    return (
        <>
            <CollapsibleHeader onClick={handleToggle}>
                {getTotalValueBySalesConsultant(type, item, columns, isOpen, isForExportPdf, null, false, true)}
            </CollapsibleHeader>
            <CollapsibleContent key={name} isOpen={isOpen} overflowVisible={overflowVisible}>
                {children}
            </CollapsibleContent>
        </>
    );
};

export default PerformanceTableRowCollapsibleContent;
