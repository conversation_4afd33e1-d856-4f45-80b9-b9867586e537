import type { RowProps } from 'antd';
import { <PERSON>, <PERSON>, Tabs, Typography } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import type { KycFieldSpecsFragment } from '../../../../../api/fragments/KYCFieldSpecs';
import type { LocalCustomerManagementModule } from '../../../../../api/types';
import { CompanyTheme, CustomerKind } from '../../../../../api/types';
import { useCompany } from '../../../../../components/contexts/CompanyContextManager';
import { useRouter } from '../../../../../components/contexts/shared';
import CheckboxField from '../../../../../components/fields/ci/CheckboxField';
import { useThemeComponents } from '../../../../../themes/hooks';
import { KycPresetFieldsRenderer } from '../../../../../utilities/kycPresets';
import type { UploadDocumentProp } from '../../../../../utilities/kycPresets/shared';
import { CheckboxContainer } from '../../../../shared/CIPage/ConsentAndDeclarations/shared';
import ResetKYCButton from '../../../../shared/JourneyPage/CustomerDetails/ResetKYCButton';
import { allowedCountryForSections } from '../../../../shared/JourneyPage/CustomerDetails/shared';
import { Title } from '../../../EventApplicationEntrypoint/ApplicantForm/shared';
import GroupedCustomerDetails from '../../../StandardApplicationEntrypoint/KYCPage/shared';
import { FeatureKYCPageLabel } from '../shared';

const HeaderContainer = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
`;

export const OccupySpace = styled.div`
    margin-bottom: 24px;
`;

// added this because of the flickering issue
// https://github.com/ant-design/ant-design/issues/43541
const StyledTabs = styled(Tabs)`
    .ant-tabs-nav-operations {
        display: none !important;
    }
`;

export type CustomerDetailsProps = {
    kycPresets: KycFieldSpecsFragment[];
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    setIsCorporate?: Dispatch<SetStateAction<boolean>>;
    customerKind: CustomerKind;
    showTabs: boolean;
    hasGuarantorPreset: boolean;
    setPrefill: Dispatch<SetStateAction<boolean>>;
    showResetButton?: boolean;
    resetFormHandler?: () => void;
    typeKYCPageTitle: FeatureKYCPageLabel;
    fieldsColSpan?: { md: number; sm: number; xs: number };
    gutter?: RowProps['gutter'];
} & UploadDocumentProp;

const CustomerDetails = ({
    kycPresets,
    kycExtraSettings,
    setIsCorporate,
    customerKind,
    showTabs,
    hasGuarantorPreset,
    setPrefill,
    showResetButton = false,
    resetFormHandler = () => {},
    uploadDocument,
    removeDocument,
    typeKYCPageTitle,
    fieldsColSpan = { md: 12, sm: 24, xs: 24 },
    gutter = [16, 0],
}: CustomerDetailsProps) => {
    const { t } = useTranslation('customerDetails');

    const [currentTab, setCurrentTab] = useState<CustomerKind>(CustomerKind.Local);
    const router = useRouter();
    const company = useCompany(true);
    const { theme } = useThemeComponents();

    const countryCode = useMemo(() => {
        if (company) {
            return company.countryCode;
        }

        if (router) {
            return router.company.countryCode;
        }

        return null;
    }, [company, router]);

    const kycHeaderTitle = useMemo(() => {
        if (typeKYCPageTitle === FeatureKYCPageLabel.GiftVoucher) {
            return t('customerDetails:giftVoucher.giftVoucherCustomerDetails');
        }

        return customerKind === CustomerKind.Guarantor
            ? t('customerDetails:panelTitles.guarantorDetails')
            : t('customerDetails:panelTitles.customerDetails');
    }, [customerKind, t, typeKYCPageTitle]);

    const tabsList = useMemo(
        () =>
            showTabs && customerKind !== CustomerKind.Guarantor ? (
                <StyledTabs
                    defaultActiveKey={currentTab}
                    onChange={(key: CustomerKind) => {
                        setCurrentTab(key);
                        setIsCorporate(key === CustomerKind.Corporate);
                    }}
                    onTabClick={(key, event) => event.stopPropagation()}
                >
                    <Tabs.TabPane key={CustomerKind.Local} tab={t(`customerDetails:panelTitles.customerDetails`)} />
                    <Tabs.TabPane
                        key={CustomerKind.Corporate}
                        tab={t(`customerDetails:panelTitles.corporateDetails`)}
                    />
                </StyledTabs>
            ) : null,
        [currentTab, customerKind, setIsCorporate, showTabs, t]
    );

    return (
        <>
            <HeaderContainer>
                <Space size={10}>
                    <Title size="large">{kycHeaderTitle}</Title>
                    {showResetButton && theme !== CompanyTheme.Porsche && theme !== CompanyTheme.PorscheV3 && (
                        <ResetKYCButton onConfirm={resetFormHandler} />
                    )}
                </Space>
                {tabsList}
            </HeaderContainer>

            <Row gutter={gutter}>
                {allowedCountryForSections.includes(countryCode) ? (
                    <GroupedCustomerDetails
                        customerFields={kycPresets}
                        customerKind={customerKind}
                        kycExtraSettings={kycExtraSettings}
                        prefix="customer.fields"
                        removeDocument={removeDocument}
                        setPrefill={setPrefill}
                        uploadDocument={uploadDocument}
                    />
                ) : (
                    <KycPresetFieldsRenderer
                        colSpan={fieldsColSpan}
                        customerType="customer"
                        extraSettings={kycExtraSettings}
                        fields={kycPresets}
                        gutter={gutter}
                        markMyinfo={false}
                        prefix="customer.fields"
                        removeDocument={removeDocument}
                        uploadDocument={uploadDocument}
                    />
                )}
            </Row>

            {hasGuarantorPreset && (
                <OccupySpace>
                    <CheckboxContainer>
                        <CheckboxField name="hasGuarantor">
                            <Typography>{t('customerDetails:fields.hasGuarantor.label')}</Typography>
                        </CheckboxField>
                    </CheckboxContainer>
                </OccupySpace>
            )}
        </>
    );
};

export default CustomerDetails;
