import { Col } from 'antd';
import { useFormikContext } from 'formik';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { CustomerKind, LeadStatus } from '../../../../api/types';
import {
    useAdditionalCustomerFields,
    FeatureInUsed,
} from '../../../shared/ApplicationDetailsPage/standard/ApplicationTab/ApplicantPanel';
import AgreementsGrid from '../../../shared/CIPage/ConsentAndDeclarations/AgreementsGrid';
import { TradeInVehicleDetails } from '../../../shared/JourneyPage/CustomerDetails/TradeInVehicleItem';
import GroupedCustomerDetails from '../../StandardApplicationEntrypoint/KYCPage/shared';
import CapValueFields from '../ui/CapValueFields';
import { CollapsibleContentItem } from '../ui/ContentItem';
import FormSubSectionHeader from '../ui/FormSubSectionHeader';
import type { LeadFormValues } from '../utils/types';
import useTradeInVehicleFields from '../utils/useTradeInVehicleFields';
import { useLeadDetailsContext } from './LeadDetailsContext';
import LeadVehicleDetails from './LeadVehicleDetails';
import MainDetails from './MainDetails';
import RetainFinancingDetails, { useRetainFinancingTitle } from './RetainFinancingDetails';
import RetainVehicleDetails from './RetainVehicleDetails';
import { defaultFieldsColSpan } from './ui';

const Container = styled.div`
    display: flex;
    flex-direction: column;
    gap: 36px;
`;

const LeadForm = () => {
    const { t } = useTranslation(['launchpadLeadDetails']);
    const { lead, permissions } = useLeadDetailsContext();
    const [, setPrefill] = useState(false);
    const { businessPartnerId, customerCiamId } = useAdditionalCustomerFields(lead.customer, FeatureInUsed.Customer);
    const { values } = useFormikContext<LeadFormValues>();

    const { capValues, module } = lead;

    const kycExtraSettings = useMemo(() => {
        if (module?.__typename === 'LaunchPadModule') {
            return module.customerModule?.__typename === 'LocalCustomerManagementModule'
                ? module.customerModule.extraSettings
                : null;
        }

        return null;
    }, [module]);

    const financingDetailsTitle = useRetainFinancingTitle(lead, t);

    const { hasCurrentVehicleFields, areTradeInValuesFromMyInfo } = useTradeInVehicleFields(
        lead.customerKYC,
        values?.tradeInVehicle
    );

    return (
        <Container>
            <CollapsibleContentItem title={t('launchpadLeadDetails:form.title.mainDetails')} defaultOpen>
                <MainDetails lead={lead} />
            </CollapsibleContentItem>
            <CollapsibleContentItem title={t('launchpadLeadDetails:form.title.customerDetails')} hasMultiSections>
                <CapValueFields
                    businessPartnerId={businessPartnerId}
                    capValues={capValues}
                    colSpan={defaultFieldsColSpan}
                    customerCiamId={customerCiamId}
                />

                <GroupedCustomerDetails
                    SectionHeader={FormSubSectionHeader}
                    colSpan={defaultFieldsColSpan}
                    contentGutter={16}
                    customerFields={lead.customerKYC}
                    customerKind={CustomerKind.Local}
                    disabled={lead?.status === LeadStatus.Merged || lead?.status === LeadStatus.Merging}
                    hasUpdatePermission={permissions.hasUpdatePermission}
                    kycExtraSettings={kycExtraSettings}
                    prefix="customer.details"
                    removeDocument={() => Promise.resolve(true)}
                    setPrefill={setPrefill}
                    uploadDocument={() => {
                        throw new Error('Not implemented');
                    }}
                />
            </CollapsibleContentItem>

            {hasCurrentVehicleFields && (
                <CollapsibleContentItem title={t('launchpadLeadDetails:form.title.currentVehicle')}>
                    <Col span={24}>
                        <TradeInVehicleDetails
                            colSpan={defaultFieldsColSpan}
                            gutter={16}
                            kycPresets={lead.customerKYC}
                            showTitle={false}
                            withMyInfo={areTradeInValuesFromMyInfo}
                        />
                    </Col>
                </CollapsibleContentItem>
            )}

            {lead.vehicle && (
                <CollapsibleContentItem title={t('launchpadLeadDetails:form.title.vehicleOfInterest')}>
                    <LeadVehicleDetails lead={lead} />
                </CollapsibleContentItem>
            )}

            {lead.__typename === 'LaunchpadLead' && lead.versioning.createdBy?.__typename === 'PorscheRetain' && (
                <>
                    <CollapsibleContentItem title={t('launchpadLeadDetails:form.title.existingVehicleDetails')}>
                        <RetainVehicleDetails lead={lead} />
                    </CollapsibleContentItem>
                    <CollapsibleContentItem title={financingDetailsTitle}>
                        <RetainFinancingDetails lead={lead} />
                    </CollapsibleContentItem>
                </>
            )}

            <CollapsibleContentItem title={t('launchpadLeadDetails:form.title.consents')}>
                <Col span={24}>
                    <AgreementsGrid
                        agreements={lead.customerAgreements}
                        editable={false}
                        prefix="agreements"
                        disabled
                    />
                </Col>
            </CollapsibleContentItem>
        </Container>
    );
};

export default LeadForm;
