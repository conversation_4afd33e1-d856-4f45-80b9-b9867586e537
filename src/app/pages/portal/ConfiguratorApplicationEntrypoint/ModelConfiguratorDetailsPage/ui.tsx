/* eslint-disable max-len */
import { Button, Divider, Typography } from 'antd';
import styled, { css } from 'styled-components';
import { CalculatorGridItem } from '../../../../calculator/GridCalculator/fields/shared/withGridItem';
import breakpoints from '../../../../utilities/breakpoints';

const THEME_POPUP_TEXT_COLOR = 'var(--configurator-widget-popup-text-color, #fff)';
const THEME_POPUP_DISABLED_TEXT_COLOR = 'var(--configurator-widget-popup-disabled-text-color, #fff)';
const THEME_CALCULATOR_ACTIVE_COLOR = 'var(--configurator-widget-active-color, #fff)';
const THEME_CALCULATOR_BORDER_COLOR = 'var(--configurator-calculator-pre-summary-border-color, #fff)';
const THEME_CALCULATOR_BORDER_DISABLED_COLOR = 'var(--configurator-divider-color, rgba(255,255,255,0.5))';
const THEME_CHECKBOX_CHECKED_BG_COLOR = 'var(--checkbox-checked-inner-background-color, #fff)';
const THEME_CHECKBOX_CHECKED_BORDER_COLOR = 'var(--checkbox-checked-inner-border-color, #fff)';
const THEME_CHECKBOX_CHECKED_COLOR = 'var(--configurator-widget-checked-color, var(--ant-primary-color))';
const THEME_CHECKBOX_SUMMARY_CHECKED_BG_COLOR =
    'var(--checkbox-summary-checked-inner-background-color, var(--ant-primary-color))';
const THEME_CHECKBOX_SUMMARY_CHECKED_BORDER_COLOR =
    'var(--checkbox-summary-checked-inner-border-color, var(--ant-primary-color))';

type BasedOnSummaryProps = { isSummaryContent: boolean; isCheckboxChecked?: boolean };

export const DefaultContainer = styled.div`
    padding: 0 24px;

    @media screen and (min-width: ${breakpoints.lg}) {
        padding: 0 120px;
    }
`;

export const Title = styled.div`
    font-size: 1.5rem;
    font-weight: 900;
`;

export const SubTitle = styled.div`
    font-size: 1.25rem;
    font-weight: 900;
`;

export const SectionTitle = styled.div`
    font-size: 1.375rem;
    font-weight: 900;
    padding: 30px 0;
    color: var(--configurator-text-color, var(--ant-primary-color));
`;

export const ImageContainer = styled.div<{ isHidden?: boolean }>`
    ${({ isHidden = false }) => isHidden && 'display: none;'}
    position: relative;

    & .ant-image {
        width: 100%;
        height: 100%;
    }

    img,
    video {
        aspect-ratio: 3 / 2;
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
`;

export const ColorDescription = styled.div`
    padding-top: 28px;
    font-size: 18px;
    margin-bottom: 3rem;
`;

export const SelectionContainer = styled.div`
    display: flex;
    flex-direction: column;

    .configurator {
        padding: 0 24px 24px 24px;
        text-align: center;
        position: relative;
    }

    /* Desktop and Tablet Screen */
    @media (min-width: ${breakpoints.lg}) {
        img {
            width: 60vw;
            height: 88vh;
        }
        .configurator {
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 40vw;
            min-height: calc(100vh - 15rem);
            padding: 0 50px 24px 50px;
        }
        .configurator > h5 {
            align-self: center;
        }
    }
`;

export const WidgetBar = styled.div<{ $isOpen?: boolean }>`
    background-color: ${({ $isOpen }) =>
        $isOpen
            ? 'var(--configurator-widget-bar-open-color, #ffffff7f)'
            : 'var(--configurator-widget-bar-color, #ffffff7f)'};
    width: 100px;
    height: 5px;
    position: absolute;
    top: 8px;
    left: calc(50% - 50px);
    cursor: pointer;
`;

export const Container = styled.div`
    .ellipsis-text .ant-typography {
        color: inherit;
    }
`;

export const Content = styled.div`
    font-size: 16px;
    display: flex;
    justify-content: space-between;
`;

export const Label = styled.div`
    font-weight: 900;
`;

export const StyledDivider = styled(Divider)`
    border-color: var(--configurator-divider-color, var(--ant-primary-color));
    margin: 16px 0;
`;

export const Value = styled.div``;

export const ExpendButton = styled(Button)`
    padding: 0;
    background-color: transparent;
    color: inherit;
    border: none;
    width: 20px;
    height: 20px;

    & span {
        font-size: 14px;

        &.anticon {
            font-size: 16px;
        }
    }

    &:hover {
        background-color: transparent;
        color: inherit;
    }

    &:active {
        background-color: transparent;
        color: inherit;
    }

    &:focus {
        background-color: transparent;
        color: inherit;
    }
`;

export const PackageDetailsContainer = styled.div`
    margin-top: 16px;

    img {
        max-width: 100%;
    }
`;

export const AdditionalInfoContainer = styled.div`
    font-size: 14px;
    margin-top: 8px;
`;

export const AdditionalInfo = styled.div`
    display: flex;
    justify-content: space-between;
`;

export const PromoCodeContainer = styled.div<BasedOnSummaryProps>`
    display: flex;
    gap: 1rem;

    ${({ isSummaryContent }) =>
        isSummaryContent &&
        css`
            & input {
                background-color: #f0f0f0;
                border: none;

                &:focus {
                    border: none;
                    box-shadow: none;
                }

                ::placeholder {
                    /* Chrome, Firefox, Opera, Safari 10.1+ */
                    color: #b1b1b1;
                }

                :-ms-input-placeholder {
                    /* Internet Explorer 10-11 */
                    color: #b1b1b1;
                }

                ::-ms-input-placeholder {
                    /* Microsoft Edge */
                    color: #b1b1b1;
                }
            }
        `}
`;

export const FinancingInfo = styled.div`
    margin-top: 1rem;
    font-size: 14px;
    opacity: 0.75;
`;

const getBasedOnSummary =
    (forSummary: string, forNotSummary: string) =>
    ({ isSummaryContent }: BasedOnSummaryProps) =>
        isSummaryContent ? forSummary : forNotSummary;

export const SectionContainer = styled.div<BasedOnSummaryProps & { hideFormItemBorder?: boolean }>`
    margin-bottom: ${props => (props.isCheckboxChecked ? '50px' : '24px')};
    ${CalculatorGridItem} {
        background-color: transparent;
    }

    & .ant-form-item:not(:has(.ant-checkbox-wrapper)):not(:has(p-checkbox-wrapper)) {
        ${({ hideFormItemBorder = false }) =>
            hideFormItemBorder
                ? css`
                      border: none;
                  `
                : css`
                      border: 1px solid
                          ${getBasedOnSummary(
                              'var(--configurator-calculator-summary-border-color)',
                              THEME_CALCULATOR_BORDER_COLOR
                          )};
                  `}

        border-radius: var(--configurator-border-radius, 0.5rem);
        background-color: var(--configurator-widget-popup-calculator-field-background-color);

        // porsche v3 style
        & .porsche-v3-calculator-summary-field-wrapper {
            background: white;
        }

        & .porsche-v3-calculator-date-picker-field-wrapper .ant-picker .ant-picker-input input {
            font-weight: normal;
        }

        ${props =>
            props.isSummaryContent &&
            `& .porsche-v3-calculator-summary-field-wrapper{
                background: #eeeff2;
                .porsche-v3-calculator-display-field-wrapper{
                    background: #eeeff2;
                }
                
        }`}

        & > .ant-form-item-row {
            display: block;
            text-align: left;

            & .ant-checkbox {
                border: 0px;
            }

            & .ant-select-disabled {
                & span.ant-select-selection-item {
                    color: ${getBasedOnSummary('#000000', THEME_POPUP_DISABLED_TEXT_COLOR)};
                    font-weight: 100;
                }
            }

            & span.ant-select-selection-item {
                color: ${getBasedOnSummary('var(--ant-primary-color)', THEME_CALCULATOR_ACTIVE_COLOR)};
            }

            & > .ant-form-item-label {
                & > label {
                    color: ${getBasedOnSummary('#000000', THEME_POPUP_TEXT_COLOR)};
                    opacity: 0.75;
                    height: fit-content;
                    font-size: 14px;

                    &:after {
                        display: none;
                    }
                }
            }

            & input,
            span {
                color: ${getBasedOnSummary('var(--ant-primary-color)', THEME_CALCULATOR_ACTIVE_COLOR)};
                text-align: left;

                &.ant-typography {
                    opacity: var(--configurator-widget-popup-text-color-opacity);
                    font-weight: 100;
                }
            }

            & span:not(.gfv-container span) {
                font-size: 16px;
                font-weight: 900;
            }

            span.ant-typography(not:.ant-typography-disabled) {
                color: ${getBasedOnSummary('#000', THEME_POPUP_TEXT_COLOR)};
            }

            span.ant-typography-disabled {
                color: ${getBasedOnSummary('#000', THEME_POPUP_DISABLED_TEXT_COLOR)};
            }

            & .ant-select-arrow > svg {
                fill: ${getBasedOnSummary('var(--ant-primary-color)', THEME_CALCULATOR_ACTIVE_COLOR)};
            }

            & .ant-select-disabled,
            .ant-input-disabled {
                font-weight: 100;
                ${({ isSummaryContent }) =>
                    !isSummaryContent &&
                    css`
                        opacity: var(--configurator-widget-popup-text-color-opacity, #fff);
                        color: var(--configurator-widget-popup-disabled-text-color, #fff);
                    `}
                ${({ isSummaryContent }) => isSummaryContent && 'color: #000;'}
            }

            .ant-col:last-child {
                margin-top: 4px;
            }
        }
    }

    & .ant-checkbox-wrapper {
        & .ant-checkbox-checked {
            & > .ant-checkbox-inner {
                border-color: ${({ isSummaryContent }) =>
                    isSummaryContent
                        ? THEME_CHECKBOX_SUMMARY_CHECKED_BORDER_COLOR
                        : THEME_CHECKBOX_CHECKED_BORDER_COLOR};
                background-color: ${({ isSummaryContent }) =>
                    isSummaryContent ? THEME_CHECKBOX_SUMMARY_CHECKED_BG_COLOR : THEME_CHECKBOX_CHECKED_BG_COLOR};

                &::after {
                    border-color: ${({ isSummaryContent }) =>
                        isSummaryContent ? '#fff' : THEME_CHECKBOX_CHECKED_COLOR};
                }
            }
        }
        span:last-child:not(:first-child) {
            color: ${getBasedOnSummary('#000000', THEME_POPUP_TEXT_COLOR)};
        }
    }

    & .form-item-disabled {
        border: 1px solid ${getBasedOnSummary('rgba(0,0,0,0.15)', THEME_CALCULATOR_BORDER_DISABLED_COLOR)};
    }

    & .calculator-form-item-label.form-item-disabled {
        ${({ isSummaryContent }) =>
            !isSummaryContent &&
            `
                & .ant-form-item-label label span.ant-typography {
                    color: ${THEME_POPUP_DISABLED_TEXT_COLOR};
                }
            `}
    }

    ${({ isSummaryContent }) =>
        !isSummaryContent &&
        `
    .ellipsis-text p {
        color: ${THEME_POPUP_TEXT_COLOR};
    }
    .expand-button {
        color: ${THEME_POPUP_TEXT_COLOR};
        .expand-text {
            color: ${THEME_POPUP_TEXT_COLOR};
        }
    }`}
`;
export const TermsContainer = styled.div`
    margin: 1.25rem 0;
`;

export const Terms = styled(Typography.Link)<BasedOnSummaryProps>`
    &.ant-typography {
        color: ${getBasedOnSummary('#000', THEME_POPUP_TEXT_COLOR)};
        opacity: 0.75;
        font-size: 14px;

        &:hover,
        &:active,
        &:focus {
            color: ${getBasedOnSummary('#000', THEME_POPUP_TEXT_COLOR)};
        }
    }
`;

export const PriceDisclaimer = styled(Typography.Paragraph)`
    margin-top: 1rem;
    font-size: 14px;
    opacity: 0.75;
`;

export const DropdownContainer = styled.div`
    text-align: right;
`;

export const ModalBody = styled.div`
    display: flex;
    flex-direction: column;
    font-size: 16px;

    & label {
        font-size: 16px;
        height: 37px;
        border-bottom: 1px solid #d9d9d9;

        &:after {
            display: none;
        }
    }

    & .ant-form-item {
        margin-bottom: 1rem;
    }

    & .ant-form-item-label label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
        display: none;
    }
`;
