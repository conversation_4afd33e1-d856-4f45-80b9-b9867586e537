import { DownloadOutlined } from '@ant-design/icons';
import { But<PERSON> } from 'antd';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import type { ModuleListDataFragment } from '../../../api/fragments/ModuleListData';
import { useGetModulesListQuery } from '../../../api/queries/getModulesList';
import { ApplicationStage, ModuleRole } from '../../../api/types';
import LoadingElement from '../../../components/LoadingElement';
import { useAccountContext } from '../../../components/contexts/AccountContextManager';
import { useCompany, useCompanyContext } from '../../../components/contexts/CompanyContextManager';
import { useMultipleDealerIds } from '../../../components/contexts/DealerContextManager';
import { useLanguage } from '../../../components/contexts/LanguageContextManager';
import useDownloadOptions from '../../../components/fields/DownloadModal/useDownloadOptions';
import ConsolePageWithHeader from '../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import MobilityBookingList from '../../admin/MobilityBookingListPage/MobilityBookingList';
import ApplicationList, { ApplicationColumns } from '../ApplicationList';
import { useApplicationColumns } from '../ApplicationList/helpers';
import ApplicationDownloadModal from './ApplicationDownloadModal';
import { allOption } from './helpers';

export type ListPageInnerProps = {
    namespace: string;
    stage: ApplicationStage;
};

export const allowedCapFormatTypes: ModuleListDataFragment['__typename'][] = [
    'FinderApplicationPublicModule',
    'FinderApplicationPrivateModule',
    'EventApplicationModule',
    'ConfiguratorModule',
    'StandardApplicationModule',
];

const ListPageInner = ({ namespace, stage }: ListPageInnerProps) => {
    const [visible, setVisible] = useState(false);
    const [downloading, setDownloading] = useState(false);
    const [moduleListByApplication, setModuleListByApplication] = useState<string[]>([]);

    const company = useCompany(true);
    const { t } = useTranslation(namespace);
    const { token } = useAccountContext();
    const { companies } = useCompanyContext();
    const { dealerIds } = useMultipleDealerIds();
    const { formatOptions } = useDownloadOptions({ includeCapFormat: false, includeReportingFormat: true });
    const language = useLanguage();

    const setDefault = useCallback(() => {
        setVisible(false);
        setDownloading(false);
    }, [setVisible, setDownloading]);

    const { data, loading } = useGetModulesListQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            filter: {
                companyIds: companies.map(company => company.id),
                moduleRole: stage === ApplicationStage.Mobility ? ModuleRole.Mobility : ModuleRole.Application,
            },
        },
    });

    const modules = useMemo(() => data?.modules?.items || [], [data]);
    const moduleIds = useMemo(() => modules.map(module => module.id), [modules]);

    const applicationModuleIds = useMemo(
        () => (stage === ApplicationStage.Mobility ? moduleIds : moduleListByApplication),
        [moduleIds, moduleListByApplication, stage]
    );

    const filteredModules = useMemo(
        () =>
            (modules || []).filter(
                module =>
                    applicationModuleIds.includes(module.id) &&
                    (module.__typename === 'StandardApplicationModule' ||
                        module.__typename === 'EventApplicationModule' ||
                        module.__typename === 'ConfiguratorModule' ||
                        module.__typename === 'MobilityModule' ||
                        module.__typename === 'FinderApplicationPublicModule' ||
                        module.__typename === 'FinderApplicationPrivateModule' ||
                        module.__typename === 'LaunchPadModule')
            ),
        [applicationModuleIds, modules]
    );

    const channelModuleOption = useMemo(() => {
        const options = filteredModules.map(module => ({ label: module.displayName, value: module.id }));

        return [...(company && options.length > 1 ? [allOption] : []), ...options];
    }, [company, filteredModules]);

    const openDownloadModal = useCallback(() => {
        setVisible(true);
    }, [setVisible]);

    const extra = (
        <Button icon={<DownloadOutlined />} onClick={openDownloadModal} type="primary">
            {t('actions.download')}
        </Button>
    );

    const columnList = useApplicationColumns(stage);

    const showColumns = useMemo(() => {
        if (!company) {
            columnList.unshift(ApplicationColumns.Company);
        }

        return columnList;
    }, [company, columnList]);

    const displayDownloadFormats = useMemo(
        () =>
            [ApplicationStage.Appointment, ApplicationStage.Reservation, ApplicationStage.VisitAppointment].includes(
                stage
            ),
        [stage]
    );

    if (loading) {
        return <LoadingElement />;
    }

    return (
        <ConsolePageWithHeader extra={extra} title={t('title')}>
            <ApplicationDownloadModal
                channelModuleOption={channelModuleOption}
                currentLanguageId={language?.currentLanguageId || ''}
                dealerIds={dealerIds}
                downloading={downloading}
                formatOptions={formatOptions}
                formatVisible={displayDownloadFormats}
                loading={loading}
                modules={modules}
                namespace={namespace}
                setDefault={setDefault}
                setDownloading={setDownloading}
                stage={stage}
                token={token}
                visible={visible}
                forAdmin
                isLargeExport
            />
            {stage === ApplicationStage.Mobility ? (
                <MobilityBookingList moduleIds={moduleIds} />
            ) : (
                <ApplicationList
                    dealerIds={dealerIds}
                    moduleIds={moduleIds}
                    setModuleListByApplication={setModuleListByApplication}
                    showColumns={showColumns}
                    stage={stage}
                />
            )}
        </ConsolePageWithHeader>
    );
};
export default ListPageInner;
