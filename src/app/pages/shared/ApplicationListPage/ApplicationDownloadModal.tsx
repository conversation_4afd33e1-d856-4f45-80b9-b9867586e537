import { useToastManager } from '@porsche-design-system/components-react';
import { Form } from 'antd';
import { Formik } from 'formik';
import type { SetStateAction } from 'react';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import type { EventDataFragment } from '../../../api/fragments/EventData';
import type { ModuleListDataFragment } from '../../../api/fragments/ModuleListData';
import type { ApplicationStage } from '../../../api/types';
import DownloadModal from '../../../components/fields/DownloadModal/DownloadModal';
import { usePasswordModal } from '../../../components/fields/DownloadModal/PasswordModal';
import type { ExportFormat } from '../../../utilities/export';
import { initialDownloadParamsValues, useDownloadApplications } from './helpers';

type ApplicationDownloadModalProps = {
    token: string;
    stage: ApplicationStage;
    currentLanguageId: string;
    modules: ModuleListDataFragment[];
    event?: EventDataFragment;
    dealerIds: string[];
    channelModuleOption: { label: string; value: string }[];
    setDownloading: (value: SetStateAction<boolean>) => void;
    downloading: boolean;
    setDefault: () => void;
    visible: boolean;
    namespace?: string;
    isLargeExport?: boolean;
    formatVisible?: boolean;
    moduleVisible?: boolean;
    formatOptions?: {
        label: string;
        value: ExportFormat;
    }[];
    onChannelChange?: (value: string) => void;
    loading?: boolean;
    forAdmin?: boolean;
};

/**
 * A wrapper component for DownloadModal that incorporates the useDownloadApplications hook
 * and handles notifications internally.
 */
const ApplicationDownloadModal = ({
    token,
    stage,
    currentLanguageId,
    modules,
    event,
    dealerIds,
    channelModuleOption,
    setDownloading,
    downloading,
    setDefault,
    visible,
    namespace,
    isLargeExport = true,
    formatVisible = false,
    moduleVisible = true,
    formatOptions = [],
    onChannelChange,
    loading,
    forAdmin,
}: ApplicationDownloadModalProps) => {
    const { t } = useTranslation([namespace, 'applicationList']);
    const { addMessage } = useToastManager();
    const passwordModal = usePasswordModal();

    const notifyUser = useCallback(
        (message: string) => {
            addMessage({ text: message, state: 'success' });
        },
        [addMessage]
    );

    const onSubmit = useDownloadApplications({
        token,
        stage,
        currentLanguageId,
        modules,
        event,
        dealerIds,
        channelModuleOption,
        setDownloading,
        notifyUser,
        t,
        setDefault,
        passwordModal,
        namespace,
        isLargeExport,
    });

    return (
        <Formik initialValues={initialDownloadParamsValues} onSubmit={onSubmit}>
            {({ handleSubmit }) => (
                <Form id="downloadModalForm" name="downloadModalForm" onSubmitCapture={handleSubmit}>
                    <DownloadModal
                        channelModuleOption={channelModuleOption}
                        downloading={downloading}
                        forAdmin={forAdmin}
                        formatOptions={formatOptions}
                        formatVisible={formatVisible}
                        loading={loading}
                        moduleVisible={moduleVisible}
                        namespace={namespace}
                        onChannelChange={onChannelChange}
                        passwordModal={passwordModal}
                        setDefault={setDefault}
                        visible={visible}
                    />
                </Form>
            )}
        </Formik>
    );
};

export default ApplicationDownloadModal;
