import { useMemo } from 'react';
import { ApplicationSortingField } from '../../../api/types';

const useSortOrder = sort =>
    useMemo(() => {
        const companySort = sort.field === ApplicationSortingField.Company ? sort.orderValue : undefined;
        const appDateSort =
            sort.field === ApplicationSortingField.ApplicationDate ||
            sort.field === ApplicationSortingField.BookingSubmitted
                ? sort.orderValue
                : undefined;
        const appointmentDateSort =
            sort.field === ApplicationSortingField.AppointmentDate ? sort.orderValue : undefined;
        const identifierSort = sort.field === ApplicationSortingField.Identifier ? sort.orderValue : undefined;
        const assigneeSort = sort.field === ApplicationSortingField.Assignee ? sort.orderValue : undefined;
        const customerSort = sort.field === ApplicationSortingField.Customer ? sort.orderValue : undefined;
        const vehicleNameSort = sort.field === ApplicationSortingField.VehicleName ? sort.orderValue : undefined;
        const financeProductSort = sort.field === ApplicationSortingField.FinanceProduct ? sort.orderValue : undefined;
        const bankSort = sort.field === ApplicationSortingField.Bank ? sort.orderValue : undefined;
        const insurerSort = sort.field === ApplicationSortingField.Insurer ? sort.orderValue : undefined;
        const insurancePremiumSort =
            sort.field === ApplicationSortingField.InsurancePremium ? sort.orderValue : undefined;
        const transactionIdSort = sort.field === ApplicationSortingField.TransactionId ? sort.orderValue : undefined;
        const lastActivitySort = sort.field === ApplicationSortingField.LastActivity ? sort.orderValue : undefined;
        const moduleSort = sort.field === ApplicationSortingField.Module ? sort.orderValue : undefined;
        const applicationStatusSort =
            sort.field === ApplicationSortingField.ApplicationStatus ? sort.orderValue : undefined;
        const bookingStartDateSort =
            sort.field === ApplicationSortingField.BookingStartDate ? sort.orderValue : undefined;
        const bookingEndDateSort = sort.field === ApplicationSortingField.BookingEndDate ? sort.orderValue : undefined;
        const bookingLocationSort =
            sort.field === ApplicationSortingField.BookingLocation ? sort.orderValue : undefined;
        const totalAmountPaidSort =
            sort.field === ApplicationSortingField.TotalAmountPaid ? sort.orderValue : undefined;
        const finderVin = sort.field === ApplicationSortingField.FinderVin ? sort.orderValue : undefined;
        const leadGenFormNameSort =
            sort.field === ApplicationSortingField.LeadGenFormName ? sort.orderValue : undefined;
        const leadGenFormCampaignIdSort =
            sort.field === ApplicationSortingField.LeadGenFormCampaignId ? sort.orderValue : undefined;

        return {
            companySort,
            appDateSort,
            appointmentDateSort,
            identifierSort,
            assigneeSort,
            customerSort,
            vehicleNameSort,
            financeProductSort,
            bankSort,
            insurerSort,
            insurancePremiumSort,
            transactionIdSort,
            lastActivitySort,
            moduleSort,
            applicationStatusSort,
            bookingStartDateSort,
            bookingEndDateSort,
            totalAmountPaidSort,
            bookingLocationSort,
            finderVin,
            leadGenFormNameSort,
            leadGenFormCampaignIdSort,
        };
    }, [sort]);

export default useSortOrder;
