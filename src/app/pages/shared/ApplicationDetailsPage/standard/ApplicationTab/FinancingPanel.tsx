import { Button } from 'antd';
import { isNil } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import * as permissionKind from '../../../../../../shared/permissions';
import type { ApplicationDataFragment } from '../../../../../api/fragments/ApplicationData';
import { useApplicationDetailsExtraContext } from '../../../../../components/contexts/ApplicationDetailsExtraContext';
import hasPermissions from '../../../../../utilities/hasPermissions';
import ApplicationDetailsPanel from '../Panel';
import SectionKey from '../SectionKey';
import { isApplicationRequestForFinancing } from '../shared';
import AFCFinancingPanel from './AFCFinancingPanel';
import { useCalculatorModal } from './calculator';

export type FinancingPanelProps = {
    application: Exclude<ApplicationDataFragment, { __typename: 'LaunchpadApplication' }>;
};

const StyledRecalculationButton = styled(Button).attrs(() => ({
    to: '',
}))<{ forCI?: boolean }>`
    float: right;
    margin-right: 18px;
    margin-bottom: ${({ forCI }) => (forCI ? '8px' : '0px')};
`;

const StyledHeader = styled.div`
    display: inline-flex;
    margin-top: 4px;
`;

const FinancingPanel = ({ application }: FinancingPanelProps) => {
    const { t } = useTranslation('applicationDetails');
    const { show: openCalculator, render: renderCalculatorModal } = useCalculatorModal(application);
    const { forCI } = useApplicationDetailsExtraContext();
    const openRecalculation = useCallback(
        (event: React.MouseEvent<HTMLAnchorElement>) => {
            event.preventDefault();
            event.stopPropagation();
            openCalculator({ type: 'finance' });
        },
        [openCalculator]
    );

    // we hardcode it to not display recalculate feature until finance products and vehicle assignment is up
    const allowedRecalculation = useMemo(
        () =>
            (isApplicationRequestForFinancing(application) || application.__typename !== 'ConfiguratorApplication') &&
            hasPermissions(application.permissions, [permissionKind.allowRecalculateApplication]),
        [application]
    );

    const recalculateButton = allowedRecalculation && (
        <StyledRecalculationButton forCI={forCI} onClick={openRecalculation} type="primary">
            {t('applicationDetails:panels.application.financing.recalculate')}
        </StyledRecalculationButton>
    );

    return (
        <>
            <ApplicationDetailsPanel
                forCI={forCI}
                header={<StyledHeader>{t('applicationDetails:panels.application.financing.header')}</StyledHeader>}
                name={SectionKey.FinancingDetails}
                recalculateButton={recalculateButton}
            >
                <AFCFinancingPanel application={application} />
            </ApplicationDetailsPanel>
            {!isNil(renderCalculatorModal) && renderCalculatorModal()}
        </>
    );
};

export default FinancingPanel;
