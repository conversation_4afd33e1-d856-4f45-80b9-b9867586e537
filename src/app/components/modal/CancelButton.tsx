import Icon from '@ant-design/icons/es/components/Icon';
import { PButton } from '@porsche-design-system/components-react';
import type { ButtonProps } from 'antd';
import { CompanyTheme } from '../../api/types';
import { useThemeComponents, getButtonVariantsForModal } from '../../themes/hooks';
import CloseIcon from '../../icons/porscheCloseIcon.svg';

const CancelButton = ({ children, ...props }: Pick<ButtonProps, 'children' | 'onClick' | 'disabled' | 'style'>) => {
    const { theme, Button } = useThemeComponents();

    const variants = getButtonVariantsForModal(theme);

    switch (theme) {
        case CompanyTheme.PorscheV3: {
            return (
                <PButton {...props} className="cancel-button" icon="close" type="button" variant="secondary">
                    {children}
                </PButton>
            );
        }

        case CompanyTheme.Porsche: {
            return (
                <Button
                    {...props}
                    icon={<Icon className="cancel-button porsche-arrow" component={CloseIcon} />}
                    type="tertiary"
                >
                    {children}
                </Button>
            );
        }

        default:
            return (
                <Button {...props} key="cancel" className="cancel-button" type={variants.cancel}>
                    {children}
                </Button>
            );
    }
};

export default CancelButton;
