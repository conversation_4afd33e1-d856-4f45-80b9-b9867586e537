import { CalendarOutlined } from '@ant-design/icons';
import { Space } from 'antd';
import type { PickerProps } from 'antd/es/date-picker/generatePicker';
import type dayjs from 'dayjs';
import { useField } from 'formik';
import React, { memo, useCallback } from 'react';
import styled from 'styled-components';
import DayjsDatePicker from '../DayjsDatePicker';
import FormItem from './FormItem';

const StyledSpace = styled(Space)`
    width: 100%;
`;

const DatePicker = styled(DayjsDatePicker)<{ $readOnly: boolean }>`
    width: 100%;

    &.ant-picker {
        border-bottom: 1px line;
        border-top: none;
        border-left: none;
        border-right: none;
        border-radius: 0;
        box-shadow: none;
    }

    & .ant-picker-input > input {
        font-size: 16px;
    }

    &.ant-picker-suffix {
        font-size: 16px;
        width: 16px;
    }

    & .anticon {
        color: var(--ant-primary-color);
    }
`;

export interface DatePickerFieldProps extends Omit<PickerProps<dayjs.Dayjs>, 'value' | 'onChange'> {
    name: string;
    label?: string;
    required?: boolean;
    readOnly?: boolean;
}

const DatePickerField = ({ name, required, label, disabled, readOnly, ...props }: DatePickerFieldProps) => {
    const [field, meta, { setValue }] = useField({ name });

    const onChange = useCallback(
        date => {
            // set formik date value
            // dates will be converted to native Date object
            setValue(date ? date.toDate() : undefined);
        },
        [setValue]
    );

    return (
        <StyledSpace direction="vertical" size="middle">
            <FormItem label={label} meta={meta} name={name} required={required}>
                <DatePicker
                    data-cy={`form-date-picker-${name}`}
                    picker="date"
                    // spread props
                    {...props}
                    {...field}
                    onChange={onChange}
                    suffixIcon={<CalendarOutlined />}
                />
            </FormItem>
        </StyledSpace>
    );
};

export default memo(DatePickerField);
