import { EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import type { InputProps } from 'antd';
import { Input } from 'antd';
import { useField } from 'formik';
import { memo } from 'react';
import styled, { css } from 'styled-components';
import type { FormItemProps } from './FormItem';
import FormItem from './FormItem';
import { adminCss } from './InputField';

export interface InputFieldProps extends Omit<InputProps, 'value' | 'onChange' | 'autoComplete'> {
    name: string;
    label?: string;
    itemProps?: Omit<FormItemProps, 'label' | 'meta' | 'required' | 'children'>;
    autoComplete?: 'new-password';
    passwordRules?: string;
    forAdmin?: boolean;
}

const renderVisiblePasswordIcon = (visible: boolean) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />);

const StyledPassword = styled(Input.Password)`
    ${({ $forAdmin }: { $forAdmin?: boolean }) =>
        $forAdmin &&
        css`
            ${adminCss}

            & {
                border: 1px solid #d9d9d9;
                box-shadow: none;
            }

            &:focus,
            .ant-input-affix-wrapper-focused {
                border-color: var(--ant-primary-color);
                box-shadow: none;

                & + .ant-input-group-addon {
                    border-color: var(--ant-primary-color);
                }
            }
        `}
`;

const PasswordField = ({ name, required, label, itemProps, autoComplete, forAdmin, ...props }: InputFieldProps) => {
    const [field, meta] = useField({ name });

    return (
        <FormItem {...itemProps} label={label} meta={meta} required={required}>
            <StyledPassword
                $forAdmin={forAdmin}
                autoComplete={autoComplete || 'off'}
                iconRender={renderVisiblePasswordIcon}
                // spread props
                {...props}
                // then spread the field properties itself
                {...field}
            />
        </FormItem>
    );
};

export default memo(PasswordField);
