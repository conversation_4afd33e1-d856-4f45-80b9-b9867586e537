import { Col, Row, Typography } from 'antd';
import type { RangePickerProps } from 'antd/es/date-picker/generatePicker';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import type { PeriodPayload } from '../../../api/types';
import { CompanyTheme } from '../../../api/types';
import { useThemeComponents } from '../../../themes/hooks';
import { ExportFormat } from '../../../utilities/export';
import { DownloadFormItem, DownloadFormWrapper } from './ui';
import useDownloadFooterButtons from './useDownloadFooterButtons';

export type DownloadModalProps = {
    channelModuleOption: {
        label: string;
        value: string;
    }[];
    namespace: string;
    visible: boolean;
    downloading: boolean;
    formatVisible?: boolean;
    moduleVisible?: boolean;
    setDefault: () => void;
    onChannelChange?: (value: string) => void;
    formatOptions?: {
        label: string;
        value: ExportFormat;
    }[];
    passwordModal?: {
        render: () => JSX.Element;
        open: (value: string) => void;
        close: () => void;
    };
    loading?: boolean;
    forAdmin?: boolean;
};

export type period = {
    start?: Date;
    end?: Date;
};

export type DownloadState = {
    module: string;
    period: PeriodPayload;
    format?: ExportFormat;
};

const allowEmpty: [boolean, boolean] = [true, true];

const DownloadModal = ({
    namespace,
    visible,
    downloading,
    setDefault,
    onChannelChange,
    formatOptions = [],
    formatVisible = false,
    moduleVisible = true,
    channelModuleOption,
    loading,
    passwordModal,
    forAdmin,
}: DownloadModalProps) => {
    const { t } = useTranslation('ciModal');

    const {
        FormFields: { RangePickerField, SelectField },
        Modal,
        theme,
    } = useThemeComponents();

    const { values, errors, resetForm } = useFormikContext<DownloadState>();

    const disabledDate: RangePickerProps<Dayjs>['disabledDate'] = useCallback(value => {
        const invalidDate = dayjs().add(1, 'day');

        return dayjs(value).isSameOrAfter(invalidDate, 'day');
    }, []);

    const onClose = useCallback(() => {
        if (channelModuleOption.length !== 1) {
            resetForm();
        } else {
            resetForm({
                values: {
                    module: values.module,
                    period: { start: undefined, end: undefined },
                    format: ExportFormat.DefaultFormat,
                },
            });
        }
        setDefault();
    }, [channelModuleOption.length, resetForm, setDefault, values.module]);

    const footerButtons = useDownloadFooterButtons(!values.module && moduleVisible, downloading, onClose);

    const contentFontSize = useMemo(() => {
        switch (theme) {
            case CompanyTheme.PorscheV3:
                return '16px';
            default:
                return '14px';
        }
    }, [theme]);

    const description = moduleVisible
        ? t(`${namespace}:downloadModal.description`)
        : t(`${namespace}:downloadModal.descriptionWithoutModuleOption`);

    const sortedOptions = useMemo(
        () => channelModuleOption.sort((a, b) => a.label.localeCompare(b.label)),
        [channelModuleOption]
    );

    return (
        <>
            {passwordModal && passwordModal.render()}
            <Modal
                bodyStyle={{ maxHeight: '80vh', overflow: 'auto' }}
                footer={footerButtons}
                onCancel={onClose}
                open={visible}
                title={t(`${namespace}:downloadModal.title`)}
                width={480}
                centered
            >
                <Row gutter={[16, 16]}>
                    <Col span={24}>
                        <Typography style={{ fontSize: contentFontSize }}>{description}</Typography>
                    </Col>
                    {moduleVisible && (
                        <Col span={24}>
                            <DownloadFormWrapper $forAdmin={forAdmin}>
                                <DownloadFormItem $forAdmin={forAdmin}>
                                    <SelectField
                                        bordered={!forAdmin}
                                        loading={loading}
                                        name="module"
                                        onSelect={onChannelChange}
                                        options={sortedOptions}
                                        placeholder={t('ciModal:options.placeHolders.selectModule')}
                                        showSearch={forAdmin}
                                    />
                                </DownloadFormItem>
                            </DownloadFormWrapper>
                        </Col>
                    )}
                    {formatVisible && (
                        <Col span={24}>
                            <DownloadFormWrapper $forAdmin={forAdmin}>
                                <DownloadFormItem $forAdmin={forAdmin}>
                                    <SelectField
                                        bordered={!forAdmin}
                                        defaultValue={undefined}
                                        name="format"
                                        options={formatOptions}
                                        placeholder={t('ciModal:options.placeHolders.selectFormat')}
                                        showSearch={forAdmin}
                                    />
                                </DownloadFormItem>
                            </DownloadFormWrapper>
                        </Col>
                    )}
                    <Col span={24}>
                        <DownloadFormWrapper $forAdmin={forAdmin} $hasError={!!errors.period}>
                            <DownloadFormItem $forAdmin={forAdmin}>
                                <RangePickerField
                                    allowEmpty={allowEmpty}
                                    bordered={!forAdmin}
                                    defaultValue={undefined}
                                    disabledDate={disabledDate}
                                    label=""
                                    name="period"
                                    picker="date"
                                    showTime={false}
                                />
                            </DownloadFormItem>
                            {errors.period && (
                                <div className="error-message-container">
                                    {typeof errors.period === 'string' ? errors.period : 'Invalid date range'}
                                </div>
                            )}
                        </DownloadFormWrapper>
                    </Col>
                </Row>
            </Modal>
        </>
    );
};

export default DownloadModal;
