import type { TableProps } from 'antd';
import { useField } from 'formik';
import { memo, useCallback, useMemo } from 'react';
import { useThemeComponents } from '../../themes/hooks';
import type { FormItemProps } from './FormItem';
import FormItem from './FormItem';

export interface TableFieldProps<RecordType> extends Omit<TableProps<RecordType>, 'rowSelection'> {
    name: string;
    required?: boolean;
    label?: string;
    itemProps?: Omit<FormItemProps, 'label' | 'meta' | 'required' | 'children'>;
    rowSelection?: Omit<TableProps<RecordType>['rowSelection'], 'onChange' | 'selectedRowKeys'>;
}

const TableField = <RecordType extends object = any>({
    name,
    required,
    label,
    itemProps,
    rowSelection: rowSelectionFromProps,
    ...props
}: TableFieldProps<RecordType>) => {
    const [{ value: selectedRowKeys }, meta, { setValue }] = useField({ name });
    const { GenericTable } = useThemeComponents();

    const onChange = useCallback(
        (newSelectedRowKeys: React.Key[]) => {
            setValue(newSelectedRowKeys);
        },
        [setValue]
    );

    const rowSelection = useMemo(
        () => ({
            ...rowSelectionFromProps,
            selectedRowKeys,
            onChange,
        }),
        [rowSelectionFromProps, selectedRowKeys, onChange]
    );

    return (
        <FormItem {...itemProps} label={label} meta={meta} required={required}>
            <GenericTable<(props: TableProps<RecordType>) => JSX.Element>
                // spread props
                {...props}
                // add row selection
                rowSelection={rowSelection}
            />
        </FormItem>
    );
};

export default memo(TableField);
