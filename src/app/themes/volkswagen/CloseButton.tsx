import Icon from '@ant-design/icons/es/components/Icon';
import styled from 'styled-components';
import { CloseButtonCss } from '../default/CloseButton';
import LocalButton from './Button';
import DefaultIcon from '../../icons/defaultCloseIcon.svg';

const Button = styled(LocalButton)`
    ${CloseButtonCss}
`;

const CloseButton = props => <Button {...props} icon={<Icon component={DefaultIcon} />} />;

export default CloseButton;
