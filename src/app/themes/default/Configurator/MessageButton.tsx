import Icon from '@ant-design/icons/es/components/Icon';
import type { ButtonProps } from 'antd';
import { Button } from 'antd';
import styled from 'styled-components';
import Phone from '../../../assets/ci/configurator/vw/phone.svg';

const StyledMessageButton = styled(Button)`
    background-color: rgba(255, 255, 255, 0.25);
    width: 3rem;
    height: 3rem;
    margin-left: auto;

    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;

    &:hover {
        background-color: rgba(255, 255, 255, 0.25);
    }

    &:active {
        background-color: rgba(255, 255, 255, 0.25);
    }

    &:focus {
        background-color: rgba(255, 255, 255, 0.25);
    }
`;

const ConfiguratorMessageButton = ({
    icon = <Icon component={Phone} style={{ fontSize: 24, width: 24, height: 24 }} />,
    ...props
}: ButtonProps) => <StyledMessageButton {...props} icon={icon} />;

export default ConfiguratorMessageButton;
