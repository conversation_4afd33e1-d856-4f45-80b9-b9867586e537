import Icon from '@ant-design/icons/es/components/Icon';
import makeNotificationApi from '../../makeNotificationApi';
import PorscheCloseIcon from '../../../icons/porscheCloseIcon.svg';

const PorscheInfoIcon = () => (
    <svg fill="currentcolor" height="1em" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg">
        <path d="M11 7h2v2h-2zm0 4h2v6h-2z" />
        <path d="M12 3a9 9 0 1 0 9 9 9 9 0 0 0-9-9Zm0 17a8 8 0 1 1 8-8 8 8 0 0 1-8 8Z" />
    </svg>
);

const PorscheWarningIcon = () => (
    <svg fill="currentcolor" height="1em" viewBox="0 0 24 24" width="1em" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 3 3 21h18Zm0 2.24L19.38 20H4.62Z" />
        <path d="m12.5 15 .5-5h-2l.49 5h1.01zM11 16h2v2h-2z" />
    </svg>
);

const notification = makeNotificationApi({
    toastClassName: 'porsche-theme-toast',
    toastPlacement: 'bottomLeft',
    toastLoadingIcon: <Icon component={PorscheInfoIcon} style={{ fontSize: 24 }} />,
    toastLoadingCloseIcon: <Icon component={PorscheCloseIcon} style={{ fontSize: 24 }} />,
    toastBottomGap: 0,
    bannerDisableTimer: true,
    bannerClassName: 'porsche-theme-banner',
    bannerPlacement: 'top',
    bannerTopGap: 3.5 * 16, // 3.5rem * 1rem (16px)
    bannerCloseIcon: <Icon component={PorscheCloseIcon} style={{ fontSize: 24 }} />,
    errorIcon: <Icon component={PorscheInfoIcon} style={{ fontSize: 24 }} />,
    infoIcon: <Icon component={PorscheInfoIcon} style={{ fontSize: 24 }} />,
    successIcon: <Icon component={PorscheInfoIcon} style={{ fontSize: 24 }} />,
    warningIcon: <Icon component={PorscheWarningIcon} style={{ fontSize: 24 }} />,
});

export default notification;
