import Icon from '@ant-design/icons/es/components/Icon';
import type { ButtonProps } from 'antd';
import { Modal as AntdModal } from 'antd';
import { useMemo } from 'react';
import styled from 'styled-components';
import breakpoints from '../../utilities/breakpoints';
import { useThemeComponents } from '../hooks';
import type { ModalProps } from '../types';
import CloseIcon from '../../icons/porscheCloseIcon.svg';

const StyledModal = styled(AntdModal)`
    & .ant-modal-content {
        padding: 32px;
        border-radius: 0px;
    }

    & .ant-modal-title {
        font-size: 24px;
        font-weight: bold;
    }

    & .ant-modal-header,
    & .ant-modal-footer,
    & .ant-modal-body {
        border: 0;
        padding: 0;
    }

    & .ant-modal-header {
        padding-bottom: 16px;
    }

    & .ant-modal-footer {
        display: flex;
        padding-top: 32px;
    }

    & .ant-modal-footer > button {
        min-width: 100px;
    }
`;

const FooterContainer = styled.div.attrs(() => ({
    role: 'group',
}))`
    display: flex;
    gap: clamp(8px, 0.5vw + 6px, 16px);
    flex-flow: column nowrap;
    align-items: stretch;
    width: 100%;

    & > .ant-btn + .ant-btn:not(.ant-dropdown-trigger) {
        margin-left: 0;
    }

    & > .ant-btn:not(.ant-dropdown-trigger) {
        min-width: 100px;
    }

    @media screen and (min-width: ${breakpoints.xs}) {
        flex-flow: row wrap;
        align-items: center;
    }
`;

type DefaultFooterProps = Pick<ModalProps, 'okText' | 'cancelText' | 'onOk' | 'onCancel'> & {
    okButtonProps?: Pick<ButtonProps, 'htmlType' | 'form'>;
    cancelButtonProps?: Pick<ButtonProps, 'disabled' | 'style'>;
};

const DefaultFooter = ({
    okText = 'OK',
    cancelText = 'Cancel',
    onOk = () => {},
    onCancel = () => {},
    okButtonProps,
    cancelButtonProps,
}: DefaultFooterProps) => {
    const { Button } = useThemeComponents();

    return (
        <FooterContainer>
            <Button className="ok-button" onClick={onOk} type="secondary" {...okButtonProps}>
                {okText}
            </Button>
            <Button
                icon={<Icon className="cancel-button porsche-arrow" component={CloseIcon} />}
                onClick={onCancel}
                type="tertiary"
                {...cancelButtonProps}
            >
                {cancelText}
            </Button>
        </FooterContainer>
    );
};

const Modal = ({
    children,
    okText = 'Ok',
    onOk,
    okButtonProps,
    cancelText = 'Cancel',
    onCancel,
    cancelButtonProps,
    footer: inputFooter,
    maskClosable = true,
    closable = false,
    ...props
}: ModalProps) => {
    // Destructure DefaultFooterProps properties
    const footerProps: DefaultFooterProps = { cancelText, okText, onCancel, onOk, okButtonProps, cancelButtonProps };

    /**
     * set footer to null if you don't need default footer buttons.
     * DefaultFooter will be shown only if input footer is undefined.
     * */
    const footer = useMemo(
        () => (inputFooter === undefined ? <DefaultFooter {...footerProps} /> : inputFooter),
        [inputFooter, footerProps]
    );

    return (
        <StyledModal closable={closable} footer={footer} maskClosable={maskClosable} onCancel={onCancel} {...props}>
            {children}
        </StyledModal>
    );
};

export default Modal;
