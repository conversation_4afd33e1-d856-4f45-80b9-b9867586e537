import Icon from '@ant-design/icons/es/components/Icon';
import styled from 'styled-components';
import { CloseButtonCss } from '../default/CloseButton';
import LocalButton from './Button';
import PorscheCloseIcon from '../../icons/porscheCloseIcon.svg';

const Button = styled(LocalButton)`
    ${CloseButtonCss}
`;

const CloseButton = props => <Button {...props} icon={<Icon component={PorscheCloseIcon} />} />;

export default CloseButton;
