import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import Icon from '@ant-design/icons/es/components/Icon';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';
import breakpoints from '../../utilities/breakpoints';
import type { StepsItemProps, StepsProps } from '../types';
import CheckListIcon from '../../icons/checklist.svg';

const Container = styled.div`
    display: flex;
    align-items: center;
    margin: 2rem 0px 0px 0px;
`;

const ScrollerContainer = styled.div`
    height: 1.25rem;
    display: flex;
    justify-content: flex-start;
    font-size: var(--button-font-size, 1rem);
    overflow-x: auto;
    overflow-y: hidden;
    -ms-overflow-style: none;
    scrollbar-width: none;
    ::-webkit-scrollbar {
        display: none;
    }
`;

const StepButton = styled.button<{ state: string; preventClickPreviousStep: boolean }>`
    display: flex;
    align-items: center;
    flex-basis: auto;
    padding: 0px;
    white-space: nowrap;
    border: 0px;
    background-color: transparent;
    .step-icon {
        position: relative;
    }
    .checklist-icon {
        font-size: 1.125rem;
        color: rgb(1, 138, 22);
    }
    .step-label {
        margin-right: 1rem;
        color: ${props => {
            switch (props.state) {
                case 'process':
                case 'finish':
                    return 'black';

                default:
                    return '#666';
            }
        }};
    }
    cursor: ${props => {
        switch (props.state) {
            case 'finish':
                switch (props.preventClickPreviousStep) {
                    case false:
                        return 'pointer';

                    default:
                        return 'default';
                }

            case 'disabled':
                return 'not-allowed';

            default:
                return 'default';
        }
    }};

    ${props => {
        if (props.state === 'finish' && !props.preventClickPreviousStep) {
            return `
            .step-label {
                text-decoration: underline;
            }

            &:hover {
                .checklist-icon, .step-label {
                    color: var(--ant-primary-color-hover)!important;
                    transition: color .24s ease;
                } 
            };`;
        }

        return '';
    }}
`;

const ArrowButton = styled.button<{ visible: boolean; kind: string }>`
    position: relative;
    display: none;
    @media screen and (max-width: ${breakpoints.md}) {
        display: block;
        visibility: ${props => (props.visible ? 'visible' : 'hidden')};
    }

    background: ${props =>
        props.kind === 'previous'
            ? 'linear-gradient(270deg, rgba(2,0,36,0) 0%, rgba(255,255,255,1) 50%)'
            : 'linear-gradient(90deg, rgba(2, 0, 36, 0) 0%, rgba(255, 255, 255, 1) 50%)'};
    border: 0px;
    margin: 0px;

    &:hover {
        color: var(--ant-primary-color-hover);
    }
`;

const NumberedCircle = styled.div<{ state: string }>`
    display: flex;
    border-radius: 50%;
    width: 1.125rem;
    height: 1.125rem;
    font-size: 0.65rem;
    justify-content: center;
    padding-top: 1px;
    background: ${props => (props.state === 'process' ? 'black' : 'transparent')};
    border: 1px solid ${props => (props.state === 'process' ? 'black' : '#666')};
    color: ${props => (props.state === 'process' ? 'white' : '#666')};
`;

const Label = styled.div`
    margin: 3px 0px 0px 5px;
`;

const getStepNumbering = ({ index, status }: StepsItemProps) => {
    switch (status) {
        case 'finish':
            return <Icon className="checklist-icon" component={CheckListIcon} />;

        default:
            return (
                <NumberedCircle className="step-icon" state={status}>
                    {index + 1}
                </NumberedCircle>
            );
    }
};

const Steps = ({ current, items, disableAtFinalStep = false, stepRedirectOnClick }: StepsProps) => {
    const ref = useRef(null);
    const [currentStepPosition, setCurrentStepPosition] = useState<number>(current);

    const [horizontalPosition, setHorizontalPosition] = useState(0);
    const [isMaxWidth, setIsMaxWidth] = useState(false);

    useEffect(() => {
        const element = ref.current;

        const handleScroll = () => {
            // Check the component scroll width when scrolling
            setIsMaxWidth(
                element.scrollWidth !== element.clientWidth &&
                    element.scrollWidth - element.clientWidth <= element.scrollLeft
            );
            setHorizontalPosition(element.scrollLeft);
        };

        const currentElement = element.children[`stepper-${currentStepPosition}`];

        currentElement?.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });

        element.addEventListener('scroll', handleScroll);

        return () => {
            element.removeEventListener('scroll', handleScroll);
        };
    }, [currentStepPosition]);

    const stepState = useMemo(
        () =>
            items.map(item => {
                if (item.index < current) {
                    return {
                        ...item,
                        status: 'finish',
                        icon: getStepNumbering({ index: item.index, status: 'finish' }),
                    };
                }

                if (item.index === current) {
                    return {
                        ...item,
                        status: 'process',
                        icon: getStepNumbering({ index: item.index, status: 'process' }),
                    };
                }

                return {
                    ...item,
                    status: 'wait',
                    disabled: true,
                    icon: getStepNumbering({ index: item.index, status: 'wait' }),
                };
            }),
        [current, items]
    );

    const nextButton = () => {
        const element = ref.current;
        const nextElement = element.children[`steps-${currentStepPosition + 1}`];
        setCurrentStepPosition(currentStepPosition + 1);

        nextElement?.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
    };

    const prevButton = () => {
        const element = ref.current;
        const previousElement = element.children[`steps-${currentStepPosition - 1}`];
        setCurrentStepPosition(currentStepPosition - 1);

        previousElement?.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
    };

    const handleStepClick = useCallback(
        (index: number, status: string, preventClickPreviousStep: boolean) => {
            if (status === 'finish' && !preventClickPreviousStep) {
                stepRedirectOnClick?.(index);
            }
        },
        [stepRedirectOnClick]
    );

    return (
        <Container>
            <ArrowButton kind="previous" onClick={prevButton} type="button" visible={horizontalPosition > 0}>
                <LeftOutlined />
            </ArrowButton>
            <ScrollerContainer ref={ref}>
                {stepState.length && (
                    <>
                        {stepState.map(item => {
                            const preventClickPreviousStep =
                                disableAtFinalStep && current + 1 === stepState.length
                                    ? item.index + 1 < stepState.length
                                    : false;

                            return (
                                <StepButton
                                    key={`stepper-${item.index}`}
                                    id={`stepper-${item.index}`}
                                    onClick={() => handleStepClick(item.index, item.status, preventClickPreviousStep)}
                                    preventClickPreviousStep={preventClickPreviousStep}
                                    state={item.status}
                                    type="button"
                                >
                                    {item.icon}
                                    <Label className="step-label">{item.title}</Label>
                                </StepButton>
                            );
                        })}
                    </>
                )}
            </ScrollerContainer>
            <ArrowButton kind="next" onClick={nextButton} type="button" visible={!isMaxWidth}>
                <RightOutlined />
            </ArrowButton>
        </Container>
    );
};

export default Steps;
