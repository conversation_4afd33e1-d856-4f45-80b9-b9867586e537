import Icon from '@ant-design/icons/es/components/Icon';
import type { ButtonProps } from 'antd';
import { Button } from 'antd';
import styled from 'styled-components';
import themeTokens from '../themeTokens';
import ChatIcon from './ChatIcon';

const StyledMessageButton = styled(Button)`
    background-color: #ffffff3f;
    width: 3rem;
    height: 3rem;
    margin-left: auto;

    display: flex;
    justify-content: center;
    align-items: center;
    border-color: ${themeTokens.secondaryColor};
    overflow: hidden;

    &:hover {
        background-color: rgba(255, 255, 255, 0.25);
        border-color: ${themeTokens.hoveredSecondaryColor};
    }

    &:active {
        background-color: rgba(255, 255, 255, 0.25);
        border-color: ${themeTokens.hoveredSecondaryColor};
    }

    &:focus {
        background-color: rgba(255, 255, 255, 0.25);
        border-color: ${themeTokens.hoveredSecondaryColor};
    }

    svg {
        margin: -9px;
    }
`;

const MessageButton = ({ icon = <Icon component={ChatIcon} style={{ fontSize: 62 }} />, ...props }: ButtonProps) => (
    <StyledMessageButton {...props} icon={icon} />
);

export default MessageButton;
