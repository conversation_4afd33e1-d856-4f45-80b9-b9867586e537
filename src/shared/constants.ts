export const defaultInsuranceAge = 45;
export const minimumInsuranceAge = 24;
// Signature reference https://en.wikipedia.org/wiki/List_of_file_signatures
export const hexSignature = {
    // FF D8 FF support jpg and jpeg
    image: ['89 50 4E 47 0D 0A 1A 0A', '47 49 46 38 37 61', '47 49 46 38 39 61', 'FF D8 FF'],

    pdf: ['25 50 44 46 2D'],
    document: ['50 4B 03 04', '0D 44 4F 43', 'D0 CF 11 E0 A1 B1 1A E1'],
    font: ['77 4F 46 46', '77 4F 46 32', '4F 54 54 4F', '00 01 00 00 00'],
    // signature supported : ftypisom , ftypMSNV, ftypmp42
    video: ['66 74 79 70 69 73 6F 6D', '66 74 79 70 4D 53 4E 56', '66 74 79 70 6D 70 34 32'],

    archive: ['50 4B 03 04'],
    excel: ['50 4B 03 04', 'D0 CF 11 E0 A1 B1 1A E1'],
    keyFile: [
        '2D 2D 2D 2D 2D 42 45 47 49 4E 20 43 45 52 54 49 46 49 43 41 54 45 2D 2D 2D 2D 2D',
        '2D 2D 2D 2D 2D 42 45 47 49 4E 20 43 45 52 54 49 46 49 43 41 54 45 20 52 45 51 55 45 53 54 2D 2D 2D 2D 2D',
        '2D 2D 2D 2D 2D 42 45 47 49 4E 20 50 52 49 56 41 54 45 20 4B 45 59 2D 2D 2D 2D 2D',
        '2D 2D 2D 2D 2D 42 45 47 49 4E 20 44 53 41 20 50 52 49 56 41 54 45 20 4B 45 59 2D 2D 2D 2D 2D',
        '2D 2D 2D 2D 2D 42 45 47 49 4E 20 52 45 41 20 50 52 49 56 41 54 45 20 4B 45 59 2D 2D 2D 2D 2D',
        '50 75 54 54 59 2D 55 73 65 72 2D 4B 65 79 2D 46 69 6C 65 2D 32 3A',
        '50 75 54 54 59 2D 55 73 65 72 2D 4B 65 79 2D 46 69 6C 65 2D 33 3A',
        '2D 2D 2D 2D 2D 42 45 47 49 4E 20 4F 50 45 4E 53 53 48 20 50 52 49 56 41 54 45 20 4B 45 59 2D 2D 2D 2D 2D',
        '2D 2D 2D 2D 2D 42 45 47 49 4E 20 53 53 48 32 20 4B 45 59 2D 2D 2D 2D 2D',
        '73 73 68 2d 72 73 61', // ssh-rsa
        '2D 2D 2D 2D 2D 42 45 47 49 4E 20 50 55 42 4C 49 43 20 4B 45 59 2D 2D 2D 2D 2D', // -----BEGIN PUBLIC KEY-----
        '4d 49 49', // Distinguished Encoding Rules (DER) in base64, used for TTB key files
        '2d 2d 2d 2d 2d 42 45 47 49 4e 20 50 47 50 20 50 55 42 4c 49 43 20 4b 45 59 20 42 4c 4f 43 4b 2d 2d 2d 2d', // -----BEGIN PGP PUBLIC KEY BLOCK---- // first 35 chars
        '2d 2d 2d 2d 2d 42 45 47 49 4e 20 50 47 50 20 50 52 49 56 41 54 45 20 4b 45 59 20 42 4c 4f 43 4b 2d 2d 2d', // -----BEGIN PGP PRIVATE KEY BLOCK--- // first 35 chars
    ],
};

export const ofvRVTableMappedHeaders = [
    { text: 'Model*', key: 'model' },
    { text: 'Model Year*', key: 'year' },
];
