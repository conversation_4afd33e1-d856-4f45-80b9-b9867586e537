import type Excel from 'exceljs';
import type { Response } from 'express';
import type { TermSettings } from '../../database/documents/FinanceProduct/shared';
import { makeAsteriskRed } from '../utils';

export const headCellStyle: Excel.FillPattern = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'd9d8d9' },
};

const applyHeaderStyle = (worksheet: Excel.Worksheet, headerRowNumber: number = 1) => {
    const headerRow = worksheet.getRow(headerRowNumber);

    for (let i = 1; i <= headerRow.cellCount; i++) {
        const column = worksheet.getColumn(i);
        column.style = { font: { name: 'Arial' } };
        column.width = 30;
    }
};

const applyCellStyle = (worksheet: Excel.Worksheet) => {
    for (let i = 1; i <= worksheet.columnCount; i++) {
        const column = worksheet.getColumn(i);
        column.style = { font: { name: 'Arial' } };
        column.eachCell((cell, colNumber) => {
            const cellData = cell;
            cellData.font = { name: 'Arial' };
        });
    }
};

// prepare first row for matrix lease reference table
export const prepareFirstRow = (worksheet, columns = [], headerData = []) => {
    if (columns.length < 2) {
        throw new Error('Excel columns are not supported!');
    }

    const firstRow = worksheet.getRow(1);

    // other columns except matrix column
    columns.forEach((column, index) => {
        if (index < columns.length - 2) {
            const cell = firstRow.getCell(index + 1);
            cell.value = column;
            cell.fill = headCellStyle;
            makeAsteriskRed(cell);
        }
    });

    // matrix columns
    const specialCell = worksheet.getRow(1).getCell(columns.length - 1);
    specialCell.border = {
        diagonal: { up: false, down: true, style: 'dotted' },
    };
    specialCell.alignment = {
        vertical: 'middle',
        horizontal: 'left',
        wrapText: true,
    };
    specialCell.fill = headCellStyle;
    specialCell.value = `                                  ${columns[columns.length - 1]} \n ${
        columns[columns.length - 2]
    }`;
    makeAsteriskRed(specialCell);

    // data columns
    headerData.forEach((column, index) => {
        firstRow.getCell(index + columns.length).value = column;
    });

    firstRow.height = 35;
};

export const getTermsNumber = (termSettings: TermSettings): number[] => {
    if (!termSettings) {
        return [];
    }

    const { min: termMin, max: termMax, step: termStep } = termSettings;
    if (!Number.isFinite(termMin) || !Number.isFinite(termMax) || !Number.isFinite(termStep)) {
        return [];
    }

    const results = [];
    for (let term = termMin; term <= termMax; term += termStep) {
        results.push(term);
    }

    return results;
};

export const saveToExcel = ({
    productName,
    tableName,
    worksheet,
    workbook,
    res,
}: {
    productName: string;
    tableName: string;
    worksheet: Excel.Worksheet;
    workbook: Excel.Workbook;
    res: Response;
}) => {
    const filename = encodeURI(`${productName} - ${tableName}.xlsx`);
    applyHeaderStyle(worksheet, 1);
    applyCellStyle(worksheet);

    res.set({
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    workbook.xlsx.write(res);
};
