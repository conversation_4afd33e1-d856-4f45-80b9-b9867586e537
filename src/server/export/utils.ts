import bcrypt from 'bcryptjs';
import type Excel from 'exceljs';
import type { Response } from 'express';
import { filter, flow, isString, map } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import { applyCellStyle } from '../utils/excel';
import { FormatCapPurpose } from '../utils/excel/applications/cap/types';

export const getDealerIdsFromRequest = flow(
    filter(ObjectId.isValid),
    map(it => new ObjectId(it))
);

export const validCapPurposes: string[] = [FormatCapPurpose.BP, FormatCapPurpose.BP_LEAD];

const salt = bcrypt.genSaltSync(10);

export const getPassword = async (input: any) => {
    let nonce = input;
    // expect to get a long enough nonce
    if (!isString(nonce) || nonce.length < 8) {
        nonce = nanoid();
    }
    // get only the part of the hash, see: https://en.wikipedia.org/wiki/Bcrypt
    const hash = (await bcrypt.hash(nonce, salt)).slice(-16);

    return Buffer.from(hash).toString('base64');
};

export const getPeriodFilter = (start: Date, end: Date) => ({
    ...((start || end) && {
        '_versioning.createdAt': {
            ...(start && { $gte: start }),
            ...(end && { $lte: end }),
        },
    }),
});

export const getColumns = (mappedHeaders: Record<string, string>) =>
    Object.keys(mappedHeaders).reduce(
        (array, keyValue) => [...array, { header: keyValue, key: mappedHeaders[keyValue] }],
        []
    );

export const headCellStyle: Excel.FillPattern = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'd9d8d9' },
};

export const applyHeaderStyle = (worksheet: Excel.Worksheet, headerRowNumber: number = 1) => {
    const headerRow = worksheet.getRow(headerRowNumber);

    for (let i = 1; i <= headerRow.cellCount; i++) {
        const cell = headerRow.getCell(i);
        cell.fill = headCellStyle;
        if (cell.value.toString().includes('*')) {
            cell.font = { name: 'Arial' };
            cell.value = {
                richText: [
                    { text: cell.value.toString().slice(0, -1) },
                    { font: { color: { argb: 'FFFF0000' } }, text: '*' },
                ],
            };
        }
        const column = worksheet.getColumn(i);
        column.style = { font: { name: 'Arial' } };
        column.width = 20;
    }
};

export const makeAsteriskRed = (cell: Excel.Cell) => {
    const richText: any[] = cell.value
        .toString()
        .split('*')
        .map(i => ({ text: i, font: { name: 'Arial' } }));

    for (let i = 1; i < richText.length; i += 2) {
        richText.splice(i, 0, { font: { color: { argb: 'FFFF0000', name: 'Arial' } }, text: '*' });
    }

    // eslint-disable-next-line no-param-reassign
    cell.value = {
        richText,
    };
};

export const saveToExcel = ({
    fileName,
    worksheet,
    workbook,
    res,
}: {
    fileName: string;
    worksheet: Excel.Worksheet;
    workbook: Excel.Workbook;
    res: Response;
}) => {
    const filename = encodeURI(`${fileName}.xlsx`);
    applyHeaderStyle(worksheet, 1);
    applyCellStyle(worksheet);

    res.set({
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    workbook.xlsx.write(res);
};
