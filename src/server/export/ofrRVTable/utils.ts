import { type Collection, ObjectId } from 'mongodb';
import { type OfrRVSettings, VehicleKind, type OfrRVTableCell, type Vehicle } from '../../database';
import { headCellStyle, makeAsteriskRed } from '../utils';

// prepare first row for matrix rv table
export const prepareFirstRow = (worksheet, columns: string[], headerData: number[]) => {
    if (columns.length < 2) {
        throw new Error('Excel columns are not supported!');
    }

    const firstRow = worksheet.getRow(1);

    // matrix columns
    const specialCell = worksheet.getRow(1).getCell(1);
    specialCell.border = {
        diagonal: { up: false, down: true, style: 'dotted' },
    };
    specialCell.alignment = {
        vertical: 'middle',
        horizontal: 'left',
        wrapText: true,
    };
    specialCell.fill = headCellStyle;
    specialCell.value = `             ${columns[1]}\n${columns[0]}`;
    makeAsteriskRed(specialCell);

    // data columns
    headerData.forEach((column, index) => {
        firstRow.getCell(index + columns.length).value = column;
    });

    firstRow.height = 40;
};

export const getRVTable = (rvSettings: OfrRVSettings, dealerId: string) => {
    if (dealerId) {
        const dealerOverride = rvSettings?.overrides?.find(override =>
            override.dealerId.equals(new ObjectId(dealerId))
        );
        if (dealerOverride?.table && dealerOverride?.table?.length) {
            return dealerOverride.table;
        }
    }

    return rvSettings?.table || [];
};

export const getYearNumbers = (rvTable: OfrRVTableCell[]) => {
    if (rvTable.length) {
        return [...new Set(rvTable.map(item => item.year))].sort((a, b) => b - a);
    }

    const years: number[] = [];
    const currentYear = new Date().getFullYear();

    for (let i = 0; i < 6; i++) {
        years.push(currentYear - i);
    }

    return years;
};

export const getExtraModels = async (
    vehicleCollection: Collection<Vehicle>,
    vehicleModuleId: ObjectId,
    rvTable: OfrRVTableCell[]
) => {
    const existingModelNames = new Set(rvTable.map(item => item.model));

    const vehicles = await vehicleCollection
        .find({
            moduleId: vehicleModuleId,
            _kind: VehicleKind.LocalModel,
            isDeleted: false,
            parentModelId: null,
            '_versioning.isLatest': true,
            'name.defaultValue': { $nin: [...existingModelNames] },
        })
        .sort({ order: 1 })
        .project({ 'name.defaultValue': 1, _id: 0 })
        .toArray();

    return vehicles.map(vehicle => vehicle.name?.defaultValue).filter(Boolean);
};
