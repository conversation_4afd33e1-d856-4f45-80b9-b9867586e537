import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { getOr, keyBy } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { ofvRVTableMappedHeaders } from '../../../shared/constants';
import type { RequestLocals } from '../../core/express';
import { ModuleType } from '../../database';
import getDatabaseContext from '../../database/getDatabaseContext';
import { DealerPolicyAction, ModulePolicyAction } from '../../permissions';
import { InvalidPermission } from '../../schema/errors';
import { prepareWorkbook } from '../../utils/excel';
import { saveToExcel } from '../utils';
import { getExtraModels, getRVTable, getYearNumbers, prepareFirstRow } from './utils';

type ExportOfrRVTableBody = {
    moduleId: string;
    dealerId?: string;
};

const exportOFRRVTable: RequestHandler<unknown, unknown, ExportOfrRVTableBody, {}, RequestLocals> = async (
    req,
    res,
    next
) => {
    const { moduleId, dealerId } = req.body;
    const { collections } = await getDatabaseContext();
    const { getPermissionController } = res.locals.context;
    const permissionController = await getPermissionController();

    try {
        const module = await collections.modules.findOne({
            _id: new ObjectId(moduleId),
        });

        if (!module || module._type !== ModuleType.OFRModule) {
            res.status(404).send('Not found');

            return;
        }

        if (dealerId && ObjectId.isValid(dealerId)) {
            const dealer = await collections.dealers.findOne({ _id: new ObjectId(dealerId) });
            if (!dealer) {
                res.status(404).send('Dealer not found');

                return;
            }

            if (!permissionController.dealers.hasPolicyForAction(DealerPolicyAction.Update)) {
                throw new InvalidPermission();
            }
        } else if (!permissionController.modules.mayOperateOn(module, ModulePolicyAction.Update)) {
            throw new InvalidPermission();
        }

        const vehicleModule = await collections.modules.findOne({
            companyId: module.companyId,
            _type: ModuleType.SimpleVehicleManagement,
        });

        if (!vehicleModule) {
            res.status(404).send('Vehicle module not found for the company');

            return;
        }

        const rvTable = getRVTable(module.rvSettings, dealerId);
        const yearsNumber = getYearNumbers(rvTable);
        const rvModels = [...new Set(rvTable.map(item => item.model))];
        const extraModels = await getExtraModels(collections.vehicles, vehicleModule._id, rvTable);
        const workbook = prepareWorkbook();
        const worksheet = workbook.getWorksheet(1);
        const excelColumnKeys = ofvRVTableMappedHeaders.map(i => i.text);

        prepareFirstRow(worksheet, excelColumnKeys, yearsNumber);

        const getModelAndYearKey = ({ model, year }) => `${model}-${year}`;

        const rvTableDataByModelAndYear = keyBy(getModelAndYearKey, rvTable || []);

        rvModels.forEach(model => {
            const values = yearsNumber.map(year => {
                const modelAndYearKey = getModelAndYearKey({ model, year });

                return getOr(null, `${modelAndYearKey}.value`, rvTableDataByModelAndYear);
            });

            worksheet.addRow([model, ...values]);
        });

        extraModels.forEach(model => {
            const values = yearsNumber.map(year => {
                const modelAndYearKey = getModelAndYearKey({ model, year });

                return getOr(null, `${modelAndYearKey}.value`, rvTableDataByModelAndYear);
            });

            worksheet.addRow([model, ...values]);
        });

        saveToExcel({ fileName: 'OFRTableValues', worksheet, workbook, res });
    } catch (error) {
        res.status(400).send(error);
    }
};

export default exportOFRRVTable;
