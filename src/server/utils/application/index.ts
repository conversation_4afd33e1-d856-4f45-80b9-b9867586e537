import { get, isArray, isNil, sortBy } from 'lodash/fp';
import type { Application, Lead, Module } from '../../database/documents';
import {
    ApplicationKind,
    ApplicationStage,
    ApplicationStatus,
    AuditTrailKind,
    ModuleType,
} from '../../database/documents';
import { AudienceMessage } from '../../emails/type';
import type { Loaders } from '../../loaders';
import createLoaders from '../../loaders';

// copied from app/utilities/getApplicationFileName.ts
export enum FeApplicationStage {
    Financing = 'Financing',
    Lead = 'Lead',
    Reservation = 'Reservation',
    Mobility = 'Mobility',
    Appointment = 'Appointment',
    Insurance = 'Insurance',
    VisitAppointment = 'VisitAppointment',
    TradeIn = 'TradeIn',
}

export const DEFAULT_STAGES = [
    ApplicationStage.Mobility,
    ApplicationStage.Financing,
    ApplicationStage.Reservation,
    ApplicationStage.Lead,
    ApplicationStage.Appointment,
    ApplicationStage.Insurance,
    ApplicationStage.VisitAppointment,
];

export const sortStages = (stages: ApplicationStage[]) => sortBy(stage => DEFAULT_STAGES.indexOf(stage), stages);

export const getPathByStage = (stage: ApplicationStage) => {
    switch (stage) {
        case ApplicationStage.Reservation:
            return 'reservationStage';

        case ApplicationStage.Mobility:
            return 'mobilityStage';

        case ApplicationStage.Financing:
            return 'financingStage';

        case ApplicationStage.Appointment:
            return 'appointmentStage';

        case ApplicationStage.Insurance:
            return 'insuranceStage';

        case ApplicationStage.VisitAppointment:
            return 'visitAppointmentStage';

        case ApplicationStage.TradeIn:
            return 'tradeInStage';

        case ApplicationStage.FollowUp:
            return 'followUpStage';

        default:
            return null;
    }
};

export const possibleStagePaths = Object.values(ApplicationStage).map(getPathByStage).filter(Boolean);

export const possibleStageStatusPaths = possibleStagePaths.map(path => `${path}.status`);

export const possibleStageAssigneeIdPaths = possibleStagePaths.map(path => `${path}.assigneeId`);

export const getStageStatuses = (application: Application) =>
    possibleStageStatusPaths.map(path => get(path, application)).filter(Boolean);

export const getApplicationStages = (application: Application, lead?: Lead) =>
    application.stages.map(stage => {
        const path = getPathByStage(stage);

        switch (stage) {
            case ApplicationStage.Reservation:
                return { stage, value: application.reservationStage, path };

            case ApplicationStage.Mobility:
                return { stage, value: application.mobilityStage, path };

            case ApplicationStage.Lead:
                return lead ? { stage, value: lead } : {};

            case ApplicationStage.Financing:
                return { stage, value: application.financingStage, path };

            case ApplicationStage.Appointment:
                return { stage, value: application.appointmentStage, path };

            case ApplicationStage.Insurance:
                return { stage, value: application.insuranceStage, path };

            case ApplicationStage.VisitAppointment:
                return { stage, value: application.visitAppointmentStage, path };

            case ApplicationStage.TradeIn:
                return { stage, value: application.tradeInStage, path };

            case ApplicationStage.FollowUp:
                return { stage, value: application.followUpStage, path };

            default:
                throw new Error(`invalid stage: ${stage} for application: ${application._id}`);
        }
    });

export const getApplicationStage = (
    application: Application,
    lead: Lead,
    preference: ApplicationStage | ApplicationStage[]
) => {
    const stages = isArray(preference) ? preference : [preference];

    const candidates = getApplicationStages(application);

    for (const stage of stages) {
        if (stage === ApplicationStage.Lead) {
            return { stage, value: lead };
        }

        const found = candidates.find(candidate => candidate.stage === stage);

        if (found) {
            return found;
        }
    }

    return null;
};

export type GetApplicationStageReturnType = ReturnType<typeof getApplicationStage>;

export const getApplicationIdentifier = (
    application: Application,
    lead: Lead,
    preference: ApplicationStage | ApplicationStage[]
) => getApplicationStage(application, lead, preference)?.value?.identifier;

export const getApplicationStatus = (
    application: Application,
    lead: Lead,
    preference: ApplicationStage | ApplicationStage[]
) => getApplicationStage(application, lead, preference)?.value?.status;

export const getApplicationAssigneeId = (
    application: Application,
    lead: Lead,
    preference: ApplicationStage | ApplicationStage[]
) => getApplicationStage(application, lead, preference)?.value?.assigneeId;

export const getApplicationIdentifierForAction = (application: Application, lead: Lead) =>
    getApplicationIdentifier(application, lead, [
        ApplicationStage.Mobility,
        ApplicationStage.Financing,
        ApplicationStage.Reservation,
        ApplicationStage.Lead,
        ApplicationStage.Insurance,
    ]);

// TODO: Application approved should not be covered in insurance
// currently 2 application affected by approval
// suite ids: 65310f0e7d458ec732d9cecd, 65b852e986687a5d2564f425
export const getApplicableStages = (
    statusOrAuditTrail: ApplicationStatus | AuditTrailKind,
    audience?: AudienceMessage
) => {
    switch (statusOrAuditTrail) {
        case ApplicationStatus.Cancelled:
        case AuditTrailKind.ApplicationCancelled:
        case ApplicationStatus.Completed:
        case AuditTrailKind.ApplicationCompleted:
        case AuditTrailKind.ApplicationCustomerAgreedOnCnD:
            return [
                ApplicationStage.Financing,
                ApplicationStage.Mobility,
                ApplicationStage.Lead,
                ApplicationStage.Insurance,
                ApplicationStage.Appointment,
                ApplicationStage.Reservation,
                ApplicationStage.VisitAppointment,
            ];

        case AuditTrailKind.ApplicationEmailSent:
            if (audience === AudienceMessage.Bank) {
                return [ApplicationStage.Financing];
            }
            if (audience === AudienceMessage.Insurer) {
                return [ApplicationStage.Insurance];
            }

            return [
                ApplicationStage.Financing,
                ApplicationStage.Mobility,
                ApplicationStage.Lead,
                ApplicationStage.Insurance,
                ApplicationStage.Appointment,
                ApplicationStage.Reservation,
                ApplicationStage.VisitAppointment,
            ];

        // when appointment made and when new lead captured,
        // it should not update as submitted to system when reach `SystemReceivalStep`
        case ApplicationStatus.SubmittedToSystem:
        case AuditTrailKind.ApplicationSubmittedToSystem:
        case AuditTrailKind.ApplicationResubmittedToSystem:
        case AuditTrailKind.ApplicationAmendments:
            return [ApplicationStage.Financing, ApplicationStage.Insurance];

        case AuditTrailKind.ApplicationAppointmentMade:
        case AuditTrailKind.ApplicationCheckIn:
            return [ApplicationStage.Appointment];

        case AuditTrailKind.ApplicationVisitAppointmentMade:
            return [ApplicationStage.VisitAppointment];

        case AuditTrailKind.ApplicationSubmissionToInsuranceCompanyFailed:
        case AuditTrailKind.ApplicationSubmittedToInsuranceCompany:
            return [ApplicationStage.Insurance];

        case ApplicationStatus.SigningCreationFailed:
        case ApplicationStatus.SigningInitiated:
        case ApplicationStatus.GuarantorSigningInitiated:
        case AuditTrailKind.ApplicationSigningInitiated:
        case ApplicationStatus.SigningPending:
        case ApplicationStatus.GuarantorSigningPending:
        case ApplicationStatus.SigningCompleted:
        case ApplicationStatus.GuarantorSigningCompleted:
        case AuditTrailKind.ApplicationSigningCompleted:
        case ApplicationStatus.SigningRejected:
        case ApplicationStatus.GuarantorSigningRejected:
        case AuditTrailKind.ApplicationSigningRejected:
        case ApplicationStatus.SigningTimeout:
        case AuditTrailKind.ApplicationOTPInitiated:
        case ApplicationStatus.OTPCompleted:
        case AuditTrailKind.ApplicationOTPCompleted:
            return [ApplicationStage.Financing, ApplicationStage.Insurance, ApplicationStage.Mobility];

        case ApplicationStatus.Drafted:
        case AuditTrailKind.ApplicationDrafted:
        case ApplicationStatus.ApplicantDetailsPendingUpdate:
        case ApplicationStatus.GuarantorDetailsPendingUpdate:
        case AuditTrailKind.ApplicationPendingInfoFromCustomer:
        case ApplicationStatus.ApplicantDetailsReceived:
        case ApplicationStatus.GuarantorDetailsReceived:
        case AuditTrailKind.ApplicationKYCReceived:
        case AuditTrailKind.ApplicationProceedWithCustomerDevice:
            return [ApplicationStage.Financing, ApplicationStage.Insurance];

        case ApplicationStatus.Declined:
        case AuditTrailKind.ApplicationDeclined:
        case ApplicationStatus.Approved:
        case AuditTrailKind.ApplicationApproved:
        case ApplicationStatus.PendingDisbursement:
        case AuditTrailKind.ApplicationPendingDisbursement:
        case AuditTrailKind.RequestDisbursement:
        case AuditTrailKind.RequestDisbursementFail:
        case ApplicationStatus.SubmittedToBank:
        case AuditTrailKind.ApplicationSubmittedToBank:
        case AuditTrailKind.ApplicationResubmittedToBank:
        case ApplicationStatus.SubmissionToBankFailed:
        case AuditTrailKind.ApplicationSubmissionToBankFailed:
        case AuditTrailKind.ApplicationResubmissionToBankFailed:
        case ApplicationStatus.BankReviewInProgress:
        case AuditTrailKind.ApplicationCancellationToBankFailed:
        case AuditTrailKind.RequestReleaseLetter:
        case AuditTrailKind.ApplicationExpired:
        case AuditTrailKind.ApplicationRetrieveStatus:
            return [ApplicationStage.Financing];

        case ApplicationStatus.PaymentCompleted:
        case AuditTrailKind.ApplicationPaymentCompleted:
        case AuditTrailKind.ApplicationPaymentPending:
        case ApplicationStatus.PaymentPending:
        case AuditTrailKind.ApplicationPaymentCapturing:
        case ApplicationStatus.PaymentFailed:
        case AuditTrailKind.ApplicationPaymentFailed:
        case ApplicationStatus.PaymentTimeout:
        case AuditTrailKind.ApplicationPaymentRefunded:
        case AuditTrailKind.ApplicationPaymentSkipped:
        case ApplicationStatus.PaymentReceived:
        case ApplicationStatus.PaymentReceivedOffline:
        case AuditTrailKind.ApplicationApplyForReservationCreation:
            return [ApplicationStage.Reservation];

        case ApplicationStatus.CheckIn:
            return [ApplicationStage.Mobility, ApplicationStage.Appointment];

        case ApplicationStatus.Shared:
        case AuditTrailKind.ApplicationShared:
            return [ApplicationStage.Lead];

        case AuditTrailKind.ApplicationApplyForFinanceCreation:
        case ApplicationStatus.FinancingRequest:
        case AuditTrailKind.ApplicationFinancingRequested:
            return [ApplicationStage.Financing];

        case ApplicationStatus.InsuranceApproved:
        case ApplicationStatus.InsuranceCancelled:
        case ApplicationStatus.InsuranceDeclined:
        case AuditTrailKind.InsuranceApplicationApproved:
        case AuditTrailKind.InsuranceApplicationCancelled:
        case AuditTrailKind.InsuranceApplicationDeclined:
        case AuditTrailKind.ApplicationApplyForInsuranceCreation:
            return [ApplicationStage.Insurance];

        case ApplicationStatus.NewAppointment:
            return [ApplicationStage.Appointment, ApplicationStage.VisitAppointment];

        case ApplicationStatus.TestDriveCompleted:
        case ApplicationStatus.TestDriveStarted:
        case ApplicationStatus.TestDriveSigningCompleted:
        case ApplicationStatus.TestDriveSigningInitiated:
        case ApplicationStatus.TestDriveSigningPending:
        case ApplicationStatus.TestDriveSigningRejected:
        case AuditTrailKind.ApplicationTestDriveStartedAuditTrail:
        case AuditTrailKind.ApplicationTestDriveEndedAuditTrail:
        case AuditTrailKind.ApplicationTestDriveOTPInitiated:
        case AuditTrailKind.ApplicationTestDriveOTPCompleted:
        case AuditTrailKind.ApplicationTestDriveSigningInitiated:
        case AuditTrailKind.ApplicationTestDriveSigningRejected:
        case AuditTrailKind.ApplicationTestDriveSigningCompleted:
        case AuditTrailKind.ApplicationTestDriveCnDAuditTrail:
        case AuditTrailKind.ApplicationTestDriveKYCAuditTrail:
            return [ApplicationStage.Appointment];

        case ApplicationStatus.Contacted:
        case ApplicationStatus.BookingConfirmed:
        case ApplicationStatus.AppointmentCheckIn:
        case AuditTrailKind.ApplicationContacted:
        case AuditTrailKind.ApplicationConfirmedBookingAuditTrail:
        case AuditTrailKind.ApplicationShowroomVisitCnDAuditTrail:
            return [ApplicationStage.Appointment, ApplicationStage.VisitAppointment];

        case ApplicationStatus.NewLead:
            return [ApplicationStage.Lead];

        case ApplicationStatus.NewMobility:
        case AuditTrailKind.BookingSubmitted:
        case AuditTrailKind.BookingAmended:
            return [ApplicationStage.Mobility];

        case ApplicationStatus.TradeInPending:
        case ApplicationStatus.TradeInQuoted:
            return [ApplicationStage.TradeIn];

        case ApplicationStatus.FollowUpCancelled:
        case ApplicationStatus.FollowUpConfirmed:
        case ApplicationStatus.FollowUpPlanned:
            return [ApplicationStage.FollowUp];
    }

    return [];
};

export const getApplicationLogStages = (
    application: Application,
    auditTrail: AuditTrailKind,
    desired?: ApplicationStage[] | null,
    audience?: AudienceMessage
) => {
    const applicable = getApplicableStages(auditTrail, audience);

    const stages = desired ? desired.filter(stage => application.stages.includes(stage)) : application.stages;

    return stages.filter(stage => applicable.includes(stage));
};

export const getStatusUpdates = (
    application: Application,
    lead: Lead,
    status: ApplicationStatus,
    desired?: ApplicationStage[] | null
): Record<string, ApplicationStatus> => {
    const stages = getApplicationStages(application);

    const applicable = getApplicableStages(status);

    return stages.reduce(
        (acc: Record<string, ApplicationStatus>, { stage, path }) => {
            if (applicable.includes(stage) && (isNil(desired) || desired.includes(stage))) {
                return { ...acc, [`${path}.status`]: status };
            }

            return acc;
        },
        {} as Record<string, ApplicationStatus>
    );
};

export const canSkipDeposit = async ({
    application,
    applicationModule,
    loaders = createLoaders(),
}: {
    application: Application;
    applicationModule: Module;
    loaders: Loaders;
}): Promise<boolean> =>
    (application.kind === ApplicationKind.Event &&
        (await loaders.eventById.load(application.eventId))?.skipForDeposit) ||
    (applicationModule._type === ModuleType.StandardApplicationModule ||
    applicationModule._type === ModuleType.ConfiguratorModule ||
    applicationModule._type === ModuleType.FinderApplicationPrivateModule ||
    applicationModule._type === ModuleType.FinderApplicationPublicModule
        ? applicationModule.skipForDeposit
        : false);

// convert FE application stage to BE application stage
export const getBEApplicationStage = (stage: FeApplicationStage): ApplicationStage => {
    switch (stage) {
        case FeApplicationStage.Financing:
            return ApplicationStage.Financing;

        case FeApplicationStage.Lead:
            return ApplicationStage.Lead;

        case FeApplicationStage.Reservation:
            return ApplicationStage.Reservation;

        case FeApplicationStage.Appointment:
            return ApplicationStage.Appointment;

        case FeApplicationStage.Insurance:
            return ApplicationStage.Insurance;

        case FeApplicationStage.Mobility:
            return ApplicationStage.Mobility;

        case FeApplicationStage.VisitAppointment:
            return ApplicationStage.VisitAppointment;

        default:
            throw new Error(`Invalid stage from getting BE application stage: ${stage}`);
    }
};

export const getApplicationType = (stage: ApplicationStage) => {
    switch (stage) {
        case ApplicationStage.Financing:
            return 'Application';

        case ApplicationStage.Lead:
            return 'Lead';

        case ApplicationStage.Reservation:
            return 'Reservation';

        case ApplicationStage.Mobility:
            return 'Mobility';

        case ApplicationStage.Appointment:
            return 'Test Drive';

        case ApplicationStage.Insurance:
            return 'Insurance';

        case ApplicationStage.VisitAppointment:
            return 'Showroom Visit';

        default:
            throw new Error('Invalid stage from getting application type');
    }
};

// Generate mapping from ApplicationStage to applicable ApplicationStatus values
const generateStageToStatusMapping = (): Record<ApplicationStage, ApplicationStatus[]> => {
    const mapping: Record<ApplicationStage, ApplicationStatus[]> = {
        [ApplicationStage.Lead]: [],
        [ApplicationStage.Financing]: [],
        [ApplicationStage.Reservation]: [],
        [ApplicationStage.Mobility]: [],
        [ApplicationStage.Appointment]: [],
        [ApplicationStage.Insurance]: [],
        [ApplicationStage.VisitAppointment]: [],
        [ApplicationStage.TradeIn]: [],
        [ApplicationStage.FollowUp]: [],
    };

    Object.values(ApplicationStatus).forEach(status => {
        const applicableStages = getApplicableStages(status);
        applicableStages.forEach(stage => {
            if (!mapping[stage].includes(status)) {
                mapping[stage].push(status);
            }
        });
    });

    return mapping;
};

export const STAGE_TO_STATUS_MAPPING = generateStageToStatusMapping();
