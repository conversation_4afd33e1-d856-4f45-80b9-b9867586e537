import { Stream } from 'stream';
import dayjs from 'dayjs';
import Excel, { FillPattern, Worksheet } from 'exceljs';
import { TFunction } from 'i18next';
import { difference, filter, flatMap, flow, groupBy, isEmpty, isNil, map, range } from 'lodash/fp';
import { AuthorKind, LanguagePack, TranslatedString } from '../../database/documents';
import { FpTableType } from '../../schema/resolvers/enums';
import { COMPANY_ROW_INDEX, DATA_ROW_INDEX, DATA_ROW_INDEX_BMW } from './constants';
import { StringMap } from './types';

const headCellStyle: FillPattern = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'd9d8d9' },
};

export const getLastModifiedUser = (t: TFunction, updatedAt: Date, author: string, timeZone?: string) =>
    !isNil(timeZone)
        ? `${dayjs(updatedAt).tz(timeZone).format(t('common:formats.dateTimeFormat'))} by ${author}`
        : `${dayjs(updatedAt).format(t('common:formats.dateTimeFormat'))} by ${author}`;

export const getPublishedSetting = (publishedSetting: string): boolean | null => {
    switch (publishedSetting.toLowerCase()) {
        case 'yes':
            return true;
        case 'no':
            return false;
        default:
            return null;
    }
};

export const getAuthorText = (t: TFunction, authorKind: AuthorKind, name?: string) => {
    switch (authorKind) {
        case AuthorKind.User:
            return name;

        case AuthorKind.Customer:
            return name ? t('common:author.customer.withName', { name }) : t('common:author.customer.withoutName');

        case AuthorKind.PorscheRetain:
            return t('common:author.porscheRetain');

        default:
            return t('common:author.system');
    }
};

export const setPublishedSetting = (isActive: boolean) => (isActive ? 'YES' : 'NO');

export const getLanguages = (languages: string[]) => ['en', ...languages.filter(languageCode => languageCode !== 'en')];

export const getColumnNamesByLanguages = (
    languages: string[],
    columnPrefix: string,
    required: boolean = true
): string[] => languages.map((languageCode, index) => `${columnPrefix} (${languageCode})${required ? ' *' : ''}`);

export const getColumnValuesByLanguages = (languages: LanguagePack[], value: TranslatedString): string[] => {
    const overrideValue = [];
    for (const lang of languages) {
        if (value.overrides.length === 0) {
            overrideValue.push('');
        } else {
            const res = value.overrides.filter(overrideData => overrideData.languagePackId.equals(lang._id));

            overrideValue.push(res.length > 0 ? res[0].value : '');
        }
    }

    return overrideValue;
};

export const getAllLanguagesPackColumn = (languages: LanguagePack[]): string[] =>
    languages.map(language => `${language.displayName}(${language.code}) *`);

export const getDuplicateErrors = (key: string, label: string, offset: number = 0): ((data: StringMap[]) => string[]) =>
    flow([
        (collection: StringMap[]) => collection.map((value, index) => ({ index, field: value[key] })),
        groupBy((x: { index: number; field: string }) => x.field),
        Object.entries,
        filter(([, value]) => value.length > 1),
        map(([field, values]) => `Duplicate ${label} ${field} in rows ${values.map(x => x.index + offset).join(',')}.`),
    ]);

export const getDuplicateErrorsBMW = (
    key: string,
    key1: string,
    label: string,
    label1: string,
    label2: string,
    offset: number = 0
): ((data: StringMap[]) => string[]) =>
    flow([
        (collection: StringMap[]) =>
            collection.map((value, index) => ({
                index: Number(value.rowCount),
                field: value[key].concat(' ', value[key1]),
            })),
        groupBy((x: { index: number; field: string }) => x.field),
        Object.entries,
        filter(([, value]) => value.length > 1),
        map(
            ([field, values]: [string, { index: number; field: string }[]]) =>
                `Duplicate ${label} (${label1} + ${label2}) ${field} in rows ${values.map(x => x.index).join(',')}.`
        ),
    ]);

// gets duplicate error based on subgroup
export const getDuplicateErrorsWithSubGroup = (
    parent: string,
    child: string,
    label: string,
    offset: number = 0
): ((data: StringMap[]) => string[]) =>
    flow([
        (collection: StringMap[]) =>
            collection.map((value, index) => ({ index, parent: value[parent], child: value[child] })),
        // first group by parent key
        groupBy((x: StringMap) => x.parent),
        Object.entries,
        map(([parentKey, values]) => {
            // then group by child key
            const childObject = flow([
                groupBy((x: StringMap) => x.child),
                Object.entries,
                // filter out non duplicate child keys
                filter(([, y]) => y.length > 1),
                Object.fromEntries,
            ])(values);

            return [parentKey, childObject];
        }),
        // filter out non duplicate parent keys
        filter(([, childObject]) => !isEmpty(childObject)),
        flatMap(([parentKey, childObject]) =>
            Object.entries(childObject).map(
                ([childKey, values]: [string, StringMap[]]) =>
                    `Duplicate ${label} ${childKey} for ${parentKey} in rows ${values
                        .map(x => x.index + offset)
                        .join(',')}.`
            )
        ),
    ]);

export const requiresString = (value: string, label: string, row: number): string | null => {
    if (isEmpty(value)) {
        return `${label} is empty at row ${row}.`;
    }

    return null;
};

export const requiresNumber = (value: string, label: string, row: number): string | null => {
    if (Number.isNaN(Number(value))) {
        return `Invalid ${label} at row ${row}.`;
    }

    return null;
};

export const requiresDate = (value: string, label: string, row: number): string | null => {
    const date = new Date(value);
    if (Number.isNaN(date.getTime())) {
        return `Invalid ${label} at row ${row}.`;
    }

    return null;
};

export const requiresBoolean = (value: boolean, label: string, row: number): string | null => {
    if (isNil(value)) {
        return `Invalid ${label} at row ${row}. Value should be Yes or No.`;
    }

    return null;
};

export const isEqualIgnoreCase = (value: string, other: string) => value.toLowerCase() === other.toLowerCase();

// checks if string can be converted to a number
export const isStringNumber = (value: string) => !Number.isNaN(Number(value));

export const getWorkbook = async (stream: Stream, supportedFormat: string = 'xlsx'): Promise<Excel.Workbook> => {
    const workbook = new Excel.Workbook();

    if (supportedFormat === 'xlsx') {
        await workbook.xlsx.read(stream);
    } else {
        await workbook.csv.read(stream);
    }

    return workbook;
};

export const createWorkbook = (sheetName: string, mainColumns: string[], listColumns: string[]): Excel.Workbook => {
    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.insertRow(COMPANY_ROW_INDEX - 1, mainColumns);
    worksheet.insertRow(DATA_ROW_INDEX - 1, listColumns);

    const companyRow = worksheet.getRow(COMPANY_ROW_INDEX - 1);
    const headerRow = worksheet.getRow(DATA_ROW_INDEX - 1);

    for (let i = 1; i <= companyRow.cellCount; i++) {
        const cell = companyRow.getCell(i);
        cell.fill = headCellStyle;
        worksheet.getColumn(i).width = 20;
    }

    for (let i = 1; i <= headerRow.cellCount; i++) {
        const cell = headerRow.getCell(i);
        const valueStr = cell.value?.toString() || '';
        if (valueStr.includes('*')) {
            const [valueText] = valueStr.split('*');
            cell.value = {
                richText: [
                    { text: valueText },
                    { font: { bold: true, name: 'Arial', color: { argb: 'FFFF0000' } }, text: '*' },
                ],
            };
            cell.style = { font: { name: 'Arial' } };
            cell.font = { name: 'Arial' };
        }
        cell.fill = headCellStyle;
        worksheet.getColumn(i).width = 20;
    }

    return workbook;
};

export const createWorkbookWithoutCompanyAndCountryCode = (
    sheetName: string,
    listColumns: string[]
): Excel.Workbook => {
    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet(sheetName);

    worksheet.insertRow(COMPANY_ROW_INDEX - 1, listColumns);

    const headerRow = worksheet.getRow(COMPANY_ROW_INDEX - 1);

    for (let i = 1; i <= headerRow.cellCount; i++) {
        const cell = headerRow.getCell(i);
        const valueStr = cell.value?.toString() || '';
        if (valueStr.includes('*')) {
            const [valueText] = valueStr.split('*');
            cell.value = {
                richText: [
                    { text: valueText },
                    { font: { bold: true, name: 'Arial', color: { argb: 'FFFF0000' } }, text: '*' },
                ],
            };
            cell.style = { font: { name: 'Arial' } };
            cell.font = { name: 'Arial' };
        }
        cell.fill = headCellStyle;
        worksheet.getColumn(i).width = 20;
    }

    return workbook;
};

export const createWorkbookBMW = (sheetName: string, listColumns: string[]): Excel.Workbook => {
    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet(sheetName, { views: [{ showGridLines: false }] });

    worksheet.insertRow(DATA_ROW_INDEX_BMW - 1, listColumns);

    const headerRow = worksheet.getRow(DATA_ROW_INDEX_BMW - 1);

    for (let i = 1; i <= headerRow.cellCount; i++) {
        const cell = headerRow.getCell(i);
        cell.fill = headCellStyle;
        worksheet.getColumn(i).width = 20;
    }

    return workbook;
};

export const validateCompanyData = (worksheet: Worksheet, displayName?: string, countryCode?: string): string => {
    const companyRow = worksheet.getRow(2);
    // check only if there's company or country code provided
    const companyCodeValid = !displayName || companyRow.getCell(1).text.toLowerCase() === displayName.toLowerCase();
    if (!companyCodeValid) {
        return 'Company code entered is not valid.';
    }

    const countryCodeValid = !countryCode || companyRow.getCell(2).text.toLowerCase() === countryCode.toLowerCase();
    if (!countryCodeValid) {
        return 'Country code entered is not valid.';
    }

    return '';
};

export type MappedHeaders = { key: string; text: string }[];
type RecordHeaders = Record<string, string>;

export enum TableType {
    Default = 'Default',
    Matrix = 'Matrix',
}

export const getMissingHeadersOnMatrixTable = (worksheet: Worksheet, mappedHeaders: MappedHeaders) => {
    const row = worksheet.getRow(1);
    const requiredHeaders = mappedHeaders.map(i => i.text);

    if (requiredHeaders.length < 2) {
        return [];
    }

    const texts = requiredHeaders.map((_i, index) => row.getCell(index + 1).text);

    return difference(
        requiredHeaders,
        requiredHeaders.filter(
            requiredHeader => texts.includes(requiredHeader) || `${texts[texts.length - 2]}`.includes(requiredHeader)
        )
    );
};

export const getMissingHeadersOnOrdinaryTable = (worksheet: Worksheet, mappedHeaders: RecordHeaders) => {
    const row = worksheet.getRow(1);
    const requiredHeaders = Object.keys(mappedHeaders).filter(header => header.includes('*'));

    return difference(
        requiredHeaders,
        requiredHeaders.filter(header => range(1, worksheet.columnCount + 1).some(i => row.getCell(i).text === header))
    );
};

export const getMissingHeaders = (worksheet: Worksheet, mappedHeaders: MappedHeaders, tableType: FpTableType) => {
    switch (tableType) {
        case FpTableType.Matrix: {
            return getMissingHeadersOnMatrixTable(worksheet, mappedHeaders as MappedHeaders);
        }

        default: {
            return getMissingHeadersOnOrdinaryTable(
                worksheet,
                mappedHeaders.reduce((obj, item) => Object.assign(obj, { [item.text]: item.key }), {})
            );
        }
    }
};

const getHeaderIndexes = (sheet: Excel.Worksheet, headers: Record<string, string>) => {
    const row = sheet.getRow(1);
    const headerIndexes = [];

    row.eachCell({ includeEmpty: true }, (cell, col) => {
        headerIndexes[col] = headers[cell.text];
    });

    return headerIndexes;
};

const getHeaderByKey = (headerKey: string, headers: RecordHeaders) => {
    if (!headerKey) {
        return null;
    }

    return Object.entries(headers).find(([key, value]) => value === headerKey);
};
const parseRowData = (sheet: Excel.Worksheet, row: Excel.Row, headers: RecordHeaders, skipData: boolean = false) => {
    const data = {};
    const missingRequiredFields: string[] = [];

    const headerIndexes = getHeaderIndexes(sheet, headers);
    row.eachCell({ includeEmpty: true }, (cell, col) => {
        // skip if there is no header for the colum
        const hasHeader = !isEmpty(headerIndexes[col]?.trim());
        if (hasHeader) {
            const [headerTitle] = getHeaderByKey(headerIndexes[col], headers);

            // check if required field is empty
            const isRequired = headerTitle?.includes('*');
            if (isRequired && isEmpty(cell.text?.trim())) {
                missingRequiredFields.push(headerTitle);
            }

            // if there is error, data will be no use
            if (!skipData || (missingRequiredFields.length === 0 && !isEmpty(cell.text?.trim()))) {
                data[headerIndexes[col]] =
                    !Number.isNaN(parseFloat(cell.text)) && !Number.isNaN(Number(cell.text)) ? +cell.text : cell.text;
            }
        }
    });

    return {
        data,
        missingRequiredFields,
    };
};

export const getTableData = (sheet: Excel.Worksheet, mappedHeaders: RecordHeaders) => {
    const errors: string[] = [];
    const list = [];

    // skip empty row
    sheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
        // skip header row
        if (rowNumber > 1) {
            // parse data or find any empty required fields
            const { data, missingRequiredFields } = parseRowData(sheet, row, mappedHeaders, errors.length > 0);

            if (missingRequiredFields?.length > 0) {
                errors.push(`Empty Required Field/s at row #${rowNumber}: ${missingRequiredFields.join(', ')}`);
            }

            if (!isEmpty(data)) {
                list.push(data);
            }
        }
    });

    return { errors, data: list };
};

/**
 * mappedHeaders:
 * the last item is the key and title of row data for matrix excel
 * the penultimate item is the key and title of column data for matrix excel
 */
export const getValue = text => (!Number.isNaN(parseFloat(text)) && !Number.isNaN(Number(text)) ? +text : text);

const getMatrixTableData = (sheet: Excel.Worksheet, mappedHeaders: MappedHeaders) => {
    const data = [];
    const errors: string[] = [];

    // skip empty row
    sheet.eachRow({ includeEmpty: true }, (row, rowNumber) => {
        const missingTermCol: number[] = [];
        const missingRequiredFields: string[] = [];
        row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
            // break column number
            const breakColumnNumber = mappedHeaders.length - 1;
            // skip title
            if (rowNumber === 1 && colNumber > breakColumnNumber && cell.value === null) {
                // check if miss header data
                missingTermCol.push(colNumber);
            } else if (colNumber <= breakColumnNumber && rowNumber > 1 && cell.value === null) {
                // check if miss required columns
                const headerTitle =
                    mappedHeaders[colNumber <= breakColumnNumber ? colNumber - 1 : breakColumnNumber].text;

                const isRequired = headerTitle?.includes('*');
                if (isRequired) {
                    missingRequiredFields.push(headerTitle);
                }
            } else if (rowNumber > 1 && colNumber > breakColumnNumber && cell.value !== null) {
                // fetch data
                const otherProperty = mappedHeaders.reduce(
                    (acc, item, index) =>
                        index < breakColumnNumber
                            ? {
                                  ...acc,
                                  [item.key]: getValue(row.getCell(index + 1).text),
                              }
                            : acc,
                    {}
                );

                const item = {
                    [mappedHeaders[breakColumnNumber].key]: getValue(sheet.getRow(1).getCell(colNumber).text),
                    value: getValue(cell.text),
                    ...otherProperty,
                };
                data.push(item);
            }
        });

        if (missingTermCol.length > 0) {
            errors.push(`Empty Required Field/s at row #${rowNumber}: ${missingTermCol.join(', ')}`);
        }

        if (missingRequiredFields?.length > 0) {
            errors.push(`Empty Required Field/s at row #${rowNumber}: ${missingRequiredFields.join(', ')}`);
        }
    });

    return { data, errors };
};

export const getFinanceProductTableData = (
    sheet: Excel.Worksheet,
    mappedHeaders: MappedHeaders,
    tableType: FpTableType
) => {
    switch (tableType) {
        case FpTableType.Matrix:
            return getMatrixTableData(sheet, mappedHeaders as MappedHeaders);

        default:
            return getTableData(
                sheet,
                mappedHeaders.reduce((obj, item) => Object.assign(obj, { [item.text]: item.key }), {})
            );
    }
};
