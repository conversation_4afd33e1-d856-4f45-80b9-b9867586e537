import type { ObjectId } from 'mongodb';
import type { ApplicationMarketType, DealerPriceDisclaimerSetting } from './moduleShared';
import { type Phone } from './shared';

export enum SettingId {
    DefaultLocale = 'defaultLocale',
    EncryptionKeyId = 'encryptionKeyId',
    CompanySMTP = 'companySMTP',
    CompanyTwilio = 'companyTwilio',
    DefaultEmailContext = 'defaultEmailContext',
    MyInfoSetting = 'MyInfoSetting',
    MyInfoTestKeys = 'MyInfoTestKeys',
    NamirialSetting = 'NamirialSetting',

    // bank integrations
    HlfBankIntegration = 'hlfBankIntegration',
    HlfBankV2Integration = 'hlfBankV2Integration',
    UobBankIntegration = 'uobBankIntegration',
    DbsBankIntegration = 'dbsBankIntegration',
    MaybankIntegration = 'maybankIntegration',

    // payment integrations
    AdyenPayment = 'adyenPayment',
    PorschePayment = 'porschePayment',
    FiservPayment = 'fiservPayment',
    PayGatePayment = 'paygatePayment',
    TtbPayment = 'ttbPayment',

    // live chat
    WhatsappLiveChatSetting = 'whatsappLiveChatSetting',
    UserlikeChatbotSetting = 'userlikeChatbotSetting',

    // Finder Vehicle
    FinderVehicleManagementSetting = 'finderVehicleManagementSetting',
    Autoplay = 'Autoplay',

    // Insurance Integrations
    EazyInsurerIntegration = 'eazyInsurerIntegration',

    // Remote FP Settings
    WebCalc = 'webCalc',

    // CTS Settings
    Cts = 'cts',

    // Porsche Master Data Settings
    PorscheMasterData = 'porscheMasterData',

    // Trade-In Module Settings
    TradeIn = 'tradeIn',

    // Porsche C@P Settings
    Cap = 'cap',

    // Porsche ID Settings
    PorscheId = 'porscheId',

    // Docusign
    Docusign = 'docusign',

    // Porsche Retain Module Integration
    porscheRetainModuleIntegration = 'porscheRetainModuleIntegration',

    // Vehicle Data With Porsche Code
    VehicleDataWithPorscheCodeIntegration = 'vehicleDataWithPorscheCodeIntegration',

    // allow search engines to crawl the site
    AllowSearchEngines = 'allowSearchEngines',
}

export type SettingCore<TKey extends SettingId> = {
    _id: ObjectId;
    settingId: TKey;
    date: Date;
};

export type EncryptionKeyIdSetting = SettingCore<SettingId.EncryptionKeyId> & { keyId: string };

export type DefaultLocaleSetting = SettingCore<SettingId.DefaultLocale> & { locale: string };

export type DefaultEmailContextSetting = SettingCore<SettingId.DefaultEmailContext> & {
    companyName: string;
    logoUrl: string;
};

export type CompanySMTPSetting = SettingCore<SettingId.CompanySMTP> & {
    /* Foreign Key to the company ID */
    companyId: ObjectId;

    /* SMTP setting encrypted as secret */
    secrets: {
        /* SMTP host */
        host: string;
        /* SMTP port */
        port: number;
        /* SMTP from */
        from: string;
        /* SMTP with SSL */
        isSslEnabled: boolean;
        /* SMTP with authentication */
        isAuthenticationRequired: boolean;
        /* SMTP username */
        user?: string;
        /* SMTP password */
        password?: string;
        /* SMTP Certificate, required if the SMTP uses self-signed certificate */
        certificate?: string;
    };
};

export type CompanyTwilioSetting = SettingCore<SettingId.CompanyTwilio> & {
    /* Foreign Key to the company ID */
    companyId: ObjectId;

    /* Twilio setting encrypted as secret */
    secrets: {
        /* Twilio account */
        account: string;
        /* Twilio token */
        token: string;
        /* Twilio sender */
        sender: Phone;
    };
};

export type HlfBankIntegrationSecrets = {
    allowedIps: string;

    baseUrl: string;
    submissionPartial: string;
    editionPartial: string;
    cancellationPartial: string;
    reassignPartial: string;
    privateKey: string;
    publicCert: string;
    dealerCode?: string;
};

export type HlfBankV2IntegrationSecrets = HlfBankIntegrationSecrets & {
    dealerCode: string;
};

export type UobBankIntegrationSecrets = {
    allowedIps: string;

    host: string;

    // headers
    clientId: string;
    applicationId: string;
    apiKey: string;

    // two-way ssl
    sslPrivateKey: string; // from us
    sslPublicCert: string; // from us

    // JWE
    encryptionPrivateKey: string; // from us
    encryptionPublicCert: string; // from bank

    // JWS
    signaturePrivateKey: string; // from us

    dealerCode?: string;
};

export enum DbsPayloadScheme {
    v1 = 'v1',
    v2 = 'v2',
}

export type DbsBankIntegrationSecrets = {
    allowedIps: string;

    host: string;
    orgId: string;
    apiKey: string;

    privateKey: string; // from us
    publicCert: string; // from bank

    payloadScheme: DbsPayloadScheme;

    dealerCode?: string;
};

export enum MaybankEnvironment {
    SIT = 'T',
    UAT = 'U',
    Staging = 'S',
    Production = 'P',
}

export type MaybankIntegrationSecrets = {
    allowedIps: string;

    // dealer id, e.g. SGPMVS01
    dealerId?: string;

    host: string;

    // headers
    clientId: string;
    apiKey: string;
    environment: MaybankEnvironment;

    // paired with `clientId`, used for `GetToken` endpoint
    clientSecret: string;

    // JWE
    encryptionPrivateKey: string; // from us
    encryptionPublicCert: string; // from bank

    // JWS
    signaturePrivateKey: string; // from us
    signaturePublicCert: string; // from bank

    // credentials for authentication from bank to us
    generatedId: string;
    generatedSecrets: string;

    dealerCode?: string;
};

export type HlfBankIntegrationSetting = SettingCore<SettingId.HlfBankIntegration> & {
    bankId: ObjectId;

    secrets: HlfBankIntegrationSecrets;
};

export type HlfBankV2IntegrationSetting = SettingCore<SettingId.HlfBankV2Integration> & {
    bankId: ObjectId;

    secrets: HlfBankV2IntegrationSecrets;
};

export type UobBankIntegrationSetting = SettingCore<SettingId.UobBankIntegration> & {
    bankId: ObjectId;

    secrets: UobBankIntegrationSecrets;
};

// see: https://appvantage.atlassian.net/browse/AN-964
// assumption: each `appliedInterestRate` corresponds to one set of `schemeCode` and `packageCode`
export type DbsBankIntegrationMapping = {
    appliedInterestRate: number;
    schemeCode: string;
    packageCode: string;
};

export type DbsBankIntegrationSetting = SettingCore<SettingId.DbsBankIntegration> & {
    bankId: ObjectId;

    secrets: DbsBankIntegrationSecrets;

    mappings: DbsBankIntegrationMapping[];
};

export type MaybankIntegrationSetting = SettingCore<SettingId.MaybankIntegration> & {
    bankId: ObjectId;

    secrets: MaybankIntegrationSecrets;
};

export type AdyenPaymentSetting = SettingCore<SettingId.AdyenPayment> & {
    companyId: ObjectId;

    displayName: string;

    // payment module Id
    paymentModuleId: ObjectId;

    // payment amount
    amount: number;

    // payment currency
    currency: string;

    // environment
    environment: string;

    secrets: {
        // merchant account ID
        merchantAccount: string;

        // API key
        apiKey: string;

        // client Key
        clientKey: string;

        // HMAC keys
        hmacKeys: string[];

        // notification username
        username: string;

        // notfication password
        password: string;

        // prefix
        liveEndpointUrlPrefix?: string;
    };
};

export type PorschePaymentSetting = SettingCore<SettingId.PorschePayment> & {
    displayName: string;

    companyId: ObjectId;

    // payment module Id
    paymentModuleId: ObjectId;

    // payment amount
    amount: number;

    // payment currency
    currency: string;

    // environment
    environment: string;

    secrets: {
        // api key
        apiKey: string;

        // secret Key
        secretKey: string;

        // login endpoint
        loginEndpoint: string;

        // base api endpoint
        apiEndpoint: string;

        // audience
        audience: string;

        // widget url
        widgetUrl: string;

        // webhook notification key
        webhookKey: string;

        // endpoint api version
        endpointApiVersion: string;
    };
};

export type FiservPaymentSetting = SettingCore<SettingId.FiservPayment> & {
    displayName: string;

    companyId: ObjectId;

    // payment module Id
    paymentModuleId: ObjectId;

    // payment amount
    amount: number;

    // payment currency
    currency: string;

    secrets: {
        // store name
        storeName: string;

        // secret Key
        secretKey: string;

        // payment endpoint
        paymentEndpoint: string;
    };
};

export type PayGatePaymentSetting = SettingCore<SettingId.PayGatePayment> & {
    displayName: string;

    companyId: ObjectId;

    // payment module Id
    paymentModuleId: ObjectId;

    // payment amount
    amount: number;

    // payment currency
    currency: string;

    secrets: {
        // API key
        apiKey: string;

        // Encryption Key
        encryptionKey: string;

        // payment endpoint
        paymentEndpoint: string;
    };
};

// Instead of put as boolean
// Use enum instead, so we don't need to parse it again for API usage
export enum TtbPaymentFlag3ds {
    Yes = 'Y',
    No = 'N',
}

export type TtbPaymentSetting = SettingCore<SettingId.TtbPayment> & {
    displayName: string;

    companyId: ObjectId;

    // payment module Id
    paymentModuleId: ObjectId;

    // payment amount
    amount: number;

    // payment currency
    currency: string;

    // TTB audience
    audience: string;

    // Use 3DS, 'Y' or N'
    flag3ds: TtbPaymentFlag3ds;

    secrets: {
        // API key
        apiKey: string;

        // Office ID
        officeId: string;

        // payment endpoint
        paymentEndpoint: string;

        // ARC signing private key
        signingPrivateKey: string;

        // TTB signing public key, for verification
        signingPublicKey: string;

        // ARC encryption private key, for decryption
        encryptionPrivateKey: string;

        // TTB encryption public key, for encryption
        encryptionPublicKey: string;

        // JWE key id (KID) header
        kid: string;
    };
};

export type PaymentSetting =
    | AdyenPaymentSetting
    | PorschePaymentSetting
    | TtbPaymentSetting
    | FiservPaymentSetting
    | PayGatePaymentSetting;

export type NamirialSetting = SettingCore<SettingId.NamirialSetting> & {
    namirialModuleId: ObjectId;

    secrets: {
        endpoint: string;
        apiToken: string;
    };
};

export enum MyInfoSettingVersion {
    MyInfoV3 = 'MyInfoV3',
    MyInfoV5 = 'MyInfoV5',
}

type MyInfoSettingSecretsCore<TVersion extends MyInfoSettingVersion> = {
    version: TVersion;
    clientId: string;
    baseUrl: string;
    isTestEnvironment: boolean;
};

export type MyInfoSettingSecretsV3 = MyInfoSettingSecretsCore<MyInfoSettingVersion.MyInfoV3> & {
    requestPurpose: string;
    clientSecret: string;
    privateKey: string;
    publicCertificate: string;
    pkiSignature: boolean;
};

type MyInfoKeyPair = {
    publicKey: string;
    privateKey: string;
    createdAt: Date;
};

export type MyInfoKeyPairs = {
    reserved: MyInfoKeyPair;
    active: MyInfoKeyPair;
    expired?: MyInfoKeyPair;
};

export type MyInfoSettingSecretsV5 = MyInfoSettingSecretsCore<MyInfoSettingVersion.MyInfoV5> & {
    // keys are managed by system, see `rotateMyinfoKeys` queue
    keys: {
        sign: MyInfoKeyPairs;
        encrypt: MyInfoKeyPairs;
    };
};
export type MyInfoSettingSecrets = MyInfoSettingSecretsV3 | MyInfoSettingSecretsV5;

export type MyInfoSetting = SettingCore<SettingId.MyInfoSetting> & {
    moduleId: ObjectId;

    displayName: string;

    companyId: ObjectId;
    /* Credentials for MyInfo Setting */
    secrets: MyInfoSettingSecrets;
};

export type MyInfoTestKeys = SettingCore<SettingId.MyInfoTestKeys> & {
    sign: string;
    encrypt: string;
};

export type LiveChatSetting = WhatsappLiveChatSetting | UserlikeChatbotSetting;

export type WhatsappLiveChatSetting = SettingCore<SettingId.WhatsappLiveChatSetting> & {
    displayName: string;

    // keep `companyId` to enable quick search for all settings inside certain company
    companyId: ObjectId;
    liveChatModuleId: ObjectId;

    // whatsapp livechat credentials
    secrets: {
        link: string;
    };
};

export type UserlikeChatbotSetting = SettingCore<SettingId.UserlikeChatbotSetting> & {
    displayName: string;

    // keep `companyId` to enable quick search for all settings inside certain company
    companyId: ObjectId;
    liveChatModuleId: ObjectId;

    // Userlike Chatbot credentials
    secrets: {
        script: string;
    };
};

export type FinderVehicleManagementSettingSecrets = {
    /** API Key for porsche finder service */
    apiKey: string;

    /** Username for login API */
    username: string;

    /** Password for login API */
    password: string;

    /** Porsche finder service need to authenticate, point to login API */
    loginEndpoint: string;

    /** Point to finder service to get the results */
    apiEndpoint: string;

    /** Point to finder vehicle page url */
    finderUrl: string;
};

export type FinderVehicleManagementSetting = SettingCore<SettingId.FinderVehicleManagementSetting> & {
    /** Refer to finder vehicle management module */
    moduleId: ObjectId;

    /** Allow LTA import data */
    allowLTA: boolean;

    /** Finder secrets configuration */
    secrets: FinderVehicleManagementSettingSecrets;
};

export type EazyInsurerIntegrationSettingSecrets = {
    username: string;
    password: string;
    apiUrl: string;
    sftpHost: string;
    sftpPort?: number;
    sftpUser: string;
    privateKey: string;
    certCA?: string;
};

export type EazyInsurerIntegrationSetting = SettingCore<SettingId.EazyInsurerIntegration> & {
    insuranceModuleId: ObjectId;

    secrets: EazyInsurerIntegrationSettingSecrets;
};

export type WebCalcSetting = SettingCore<SettingId.WebCalc> & {
    bankModuleId: ObjectId;

    displayName: string;

    domain: string; // `@Domain`: Domain to route the request to
    calculationUrl: string; // calculation path, e.g. https://app.vwfsag.com.tw/WebCalc_UAT/WebCalcXML
};

export type CtsSetting = SettingCore<SettingId.Cts> & {
    /** Display name for CTS setting */
    displayName: string;

    /** Identifier for CTS setting */
    identifier: string;

    /** Reference to CTS module */
    ctsModuleId: ObjectId;

    /** Reference to Financing module */
    bankModuleId: ObjectId;

    /** Finance Product assigned to CTS setting */
    financeProductSuiteIds: ObjectId[];

    /** Reference to Simple Vehicle Management or Finder Vehicle Management module */
    vehicleModuleIds: ObjectId[];

    /** Market, to determine extra fees */
    market: ApplicationMarketType;

    /** Reference to Insurance module */
    insuranceModuleId: ObjectId;

    /** Insurance Product assigned to CTS setting */
    insuranceProductSuiteIds: ObjectId[];

    /** Price disclaimer setting, can be dealer specific */
    priceDisclaimer?: DealerPriceDisclaimerSetting;

    /** Preowned price disclaimer setting, can be dealer specific */
    preownedPriceDisclaimer?: DealerPriceDisclaimerSetting;

    /* Finder Module */
    finderModuleIds: ObjectId[];

    /* Setting Active */
    isActive: boolean;
};

export enum AutoplayApiVersion {
    V3 = '3.0',
}

export type AutoplaySecret = {
    /** Autoplay API Key */
    apiKey: string;

    /** Autoplay API Token */
    apiToken: string;

    /** Autoplay API Endpoint */
    apiEndpoint: string;

    /** Autoplay API Version */
    apiVersion: AutoplayApiVersion;

    /** Concurrent number of task allowed to perform API call */
    concurrentTasks: number;

    /** Concurrent delay task allowed between number of task to perform API call */
    concurrentSeconds: number;
};

export type AutoplaySetting = SettingCore<SettingId.Autoplay> & {
    /** Refer to autoplay module */
    moduleId: ObjectId;

    /** keep `companyId` to enable quick search for all settings inside certain company */
    companyId: ObjectId;

    /** display name */
    displayName: string;

    /** Autoplay secrets configuration */
    secrets: AutoplaySecret;
};

export type PorscheMasterDataSecret = {
    // PPN ( Porsche Partner Network ) related
    ppnUrl: string;
    ppnClientId: string;
    ppnClientSecret: string;
    ppnResource: string;

    // PCCD ( Porsche Car Configuration Data ) related
    pccdUrlPrefix: string;
    pccdClientId: string;
    pccdClientSecret: string;
    pccdToken: string;
};

export type PorscheMasterDataSetting = SettingCore<SettingId.PorscheMasterData> & {
    // refer to porsche master data module
    moduleId: ObjectId;

    ggId: string;

    secrets: PorscheMasterDataSecret;
};

export type TradeInSetting = SettingCore<SettingId.TradeIn> & {
    // refer to trade-in module
    moduleId: ObjectId;

    secrets: {
        baseUrl: string;

        clientId: string;

        clientSecret: string;
    };
};

export type CapSecret = {
    /* C@P Client ID */
    clientId: string;

    /* C@P Client Secret */
    clientSecret: string;

    /* PPN App2App Client ID */
    app2appClient: string;

    /* PPN App2App Client Secret */
    app2appSecret: string;

    /* PPN App2App Resource */
    app2appResource: string;
};

export type CapSetting = SettingCore<SettingId.Cap> & {
    capModuleId: ObjectId;

    app2appHost: string;

    capBaseUrl: string;
    capGroup: string;
    capEndpointEnv: string;
    capRegion: string;
    capLanguage: string;
    capSource: string;
    capLeadProcessTypeId: string;

    /* C@P PAG Market ID */
    capPagImporterId: string;

    /* C@P Authentication Group */
    capAuthGroup: string;

    /* C@P secrets configuration */
    secrets: CapSecret;
};

export type PorscheIdSetting = SettingCore<SettingId.PorscheId> & {
    porscheIdModuleId: ObjectId;
    secrets: {
        apiKey: string;
        identityProvider: string;
        audience: string;
        userDataBaseUrl: string;
    };
};

type DocusignSecrets = {
    // base path for API calls
    basePath: string;

    // base path for authentication API calls
    authBasePath: string;
    accountId: string;
    userId: string;

    // authenticate the user to docusign
    privateKey: string;

    // id identifies the integration
    clientId: string;

    // id identifies the brand
    brandId?: string;
};
export type DocusignSetting = SettingCore<SettingId.Docusign> & {
    docusignModuleId: ObjectId;

    secrets: DocusignSecrets;
};

export type VehicleDataWithPorscheCodeIntegrationSetting =
    SettingCore<SettingId.VehicleDataWithPorscheCodeIntegration> & {
        moduleId: ObjectId;

        companyId: ObjectId;
        // VW Identity authentication base path
        authBasePath: string;
        // client id for VW Identity
        authClientId: string;
        // client secret for VW Identity
        authClientSecret: string;
        // VW Identity grantType
        grantType: string;

        basePath: string;
        // Porsche client X ID
        porscheClientXId: string;
        // Porsche client X secret
        porscheClientXSecret: string;
    };

export type PorscheRetainModuleIntegrationSetting = SettingCore<SettingId.porscheRetainModuleIntegration> & {
    /* Foreign Key to the module ID */
    moduleId: ObjectId;

    secrets: {
        notificationUrl: string;
        publicKey: string;
        privateKey: string;
        publicCertRetain: string;
    };
};

export type Setting =
    | EncryptionKeyIdSetting
    | DefaultLocaleSetting
    | CompanySMTPSetting
    | HlfBankIntegrationSetting
    | HlfBankV2IntegrationSetting
    | DefaultEmailContextSetting
    | AdyenPaymentSetting
    | PorschePaymentSetting
    | FiservPaymentSetting
    | PayGatePaymentSetting
    | TtbPaymentSetting
    | MyInfoSetting
    | MyInfoTestKeys
    | NamirialSetting
    | WhatsappLiveChatSetting
    | UserlikeChatbotSetting
    | UobBankIntegrationSetting
    | DbsBankIntegrationSetting
    | FinderVehicleManagementSetting
    | AutoplaySetting
    | MaybankIntegrationSetting
    | EazyInsurerIntegrationSetting
    | WebCalcSetting
    | CtsSetting
    | PorscheMasterDataSetting
    | TradeInSetting
    | CapSetting
    | PorscheIdSetting
    | DocusignSetting
    | PorscheRetainModuleIntegrationSetting
    | VehicleDataWithPorscheCodeIntegrationSetting
    | AllowSearchEnginesSetting
    | CompanyTwilioSetting;

export type AllowSearchEnginesSetting = SettingCore<SettingId.AllowSearchEngines> & { isAllow: boolean };
