import type { PorscheIdSetting } from '../documents/Setting';
import { SettingId } from '../documents/Setting';
import type { DatabaseContext } from '../getDatabaseContext';

export default {
    identifier: '316_addUserDataBaseUrlToPorscheIdSettings',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        // Add userDataBaseUrl field to existing Porsche ID settings based on audience
        // If audience contains "preview", use preview URL, otherwise use production URL

        // Update settings where audience contains "preview"
        await db.collection<PorscheIdSetting>('settings').updateMany(
            {
                settingId: SettingId.PorscheId,
                'secrets.userDataBaseUrl': { $exists: false },
                'secrets.audience': { $regex: /preview/i },
            },
            {
                $set: {
                    'secrets.userDataBaseUrl': 'https://api.profile.porsche-preview.com',
                },
            }
        );

        // Update settings where audience does not contain "preview"
        await db.collection<PorscheIdSetting>('settings').updateMany(
            {
                settingId: SettingId.PorscheId,
                'secrets.userDataBaseUrl': { $exists: false },
                'secrets.audience': { $not: { $regex: /preview/i } },
            },
            {
                $set: {
                    'secrets.userDataBaseUrl': 'https://api.profile.porsche.com',
                },
            }
        );
    },
};
