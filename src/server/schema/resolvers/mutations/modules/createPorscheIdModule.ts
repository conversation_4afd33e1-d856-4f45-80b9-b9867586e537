import { pick } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { type PorscheIdSetting, SettingId } from '../../../../database/documents/Setting';
import { ModuleType, type PorscheIdModule } from '../../../../database/documents/modules';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { CompanyPolicyAction } from '../../../../permissions';
import { mainQueue } from '../../../../queues';
import { getSimpleVersioningByUserForCreation } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { buildRateLimiterMiddleware, requiresLoggedUser } from '../../../middlewares';
import type { GraphQLMutationResolvers } from '../../definitions';

const mutate: GraphQLMutationResolvers['createPorscheIdModule'] = async (
    root,
    { companyId, settings },
    { getUser, getPermissionController }
) => {
    const user = await getUser();
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    if (!permissionController.companies.hasPolicyForAction(CompanyPolicyAction.CreateModule)) {
        throw new InvalidPermission();
    }

    // check company ID validity
    const company = await collections.companies.findOne({ _id: companyId });

    if (!company) {
        throw new InvalidInput({ companyId: 'Invalid company ID' });
    }

    const companyHasPorscheIdModule = await collections.modules.findOne({
        companyId,
        _type: ModuleType.PorscheIdModule,
    });

    if (companyHasPorscheIdModule) {
        throw new InvalidInput({ companyId: 'This module has exist for this company' });
    }

    const simpleVersioning = getSimpleVersioningByUserForCreation(user._id);
    const document: PorscheIdModule = {
        _id: new ObjectId(),
        _type: ModuleType.PorscheIdModule,
        companyId,
        displayName: settings.displayName,
        _versioning: simpleVersioning,
    };
    await collections.modules.insertOne(document);
    const setting: PorscheIdSetting = {
        _id: new ObjectId(),
        settingId: SettingId.PorscheId,
        date: new Date(),
        porscheIdModuleId: document._id,
        secrets: pick(['apiKey', 'identityProvider', 'audience', 'userDataBaseUrl'], settings.porscheIdSetting),
    };
    await collections.settings.insertOne(setting);

    // Automatically insert newly created Porsche ID Module ID into Configurator,
    // Public Finder Module & LCF under this company
    await collections.modules.updateMany(
        {
            companyId,
            _type: { $in: [ModuleType.ConfiguratorModule, ModuleType.FinderApplicationPublicModule] },
        },
        {
            $set: {
                porscheIdModuleId: document._id,
                isCustomerDataRetreivalByPorscheId: false,
                isPorscheIdLoginMandatory: false,
            },
        }
    );

    const companyPublicEvents = await collections.events
        .aggregate([
            {
                $lookup: {
                    from: 'modules',
                    localField: 'moduleId',
                    foreignField: '_id',
                    as: 'module',
                },
            },
            {
                $unwind: {
                    path: '$module',
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $lookup: {
                    from: 'companies',
                    localField: 'module.companyId',
                    foreignField: '_id',
                    as: 'module.company',
                },
            },
            {
                $unwind: {
                    path: '$module.company',
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $match: {
                    'module.company._id': companyId,
                    privateAccess: false,
                    isDeleted: false,
                },
            },
        ])
        .toArray();

    await collections.events.updateMany(
        { _id: { $in: companyPublicEvents.map(event => event._id) }, privateAccess: false },
        {
            $set: {
                porscheIdModuleId: document._id,
                isCustomerDataRetreivalByPorscheId: false,
                isPorscheIdLoginMandatory: false,
            },
        }
    );

    await mainQueue.add({ type: 'upsertPermissions', target: 'module', moduleId: document._id });

    return document;
};

export default buildRateLimiterMiddleware({ operation: 'createPorscheIdModule' })(requiresLoggedUser(mutate));
