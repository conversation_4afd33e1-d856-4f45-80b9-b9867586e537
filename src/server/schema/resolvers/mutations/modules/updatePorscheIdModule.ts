import { pick } from 'lodash/fp';
import { SettingId } from '../../../../database/documents/Setting';
import { ModuleType } from '../../../../database/documents/modules';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { ModulePolicyAction } from '../../../../permissions';
import { getSimpleVersioningByUserForUpdate } from '../../../../utils/versioning';
import { requiresLoggedUser } from '../../../middlewares';
import type { GraphQLMutationResolvers } from '../../definitions';

const mutation: GraphQLMutationResolvers['updatePorscheIdModule'] = async (
    root,
    { moduleId, settings },
    { getPermissionController, getUser }
) => {
    const user = await getUser();
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();
    const versioning = getSimpleVersioningByUserForUpdate(user._id);

    // check the company ID validity
    const value = await collections.modules.findOneAndUpdate(
        {
            $and: [
                { _id: moduleId, _type: ModuleType.PorscheIdModule },
                permissionController.modules.getFilterQueryForAction(ModulePolicyAction.Update),
            ],
        },
        {
            $set: {
                ...versioning,
                displayName: settings.displayName,
            },
        },
        { returnDocument: 'after' }
    );

    await collections.settings.updateOne(
        {
            settingId: SettingId.PorscheId,
            porscheIdModuleId: moduleId,
        },
        {
            $set: {
                secrets: pick(['apiKey', 'identityProvider', 'audience', 'userDataBaseUrl'], settings.porscheIdSetting),
            },
        }
    );

    return value;
};

export default requiresLoggedUser(mutation);
