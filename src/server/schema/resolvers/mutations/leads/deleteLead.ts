import { LeadStatus } from '../../../../database/documents/Lead';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { LeadPolicyAction } from '../../../../permissions';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';

/*
Logic for deleting merged leads:
Main lead A1
Merge B1 => A1, C1 => A1
Deleting A1, B1, or C1 should delete all of them.
*/
const resolver: GraphQLMutationResolvers['deleteLead'] = async (root, { leadId }, { getPermissionController }) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const lead = await collections.leads.findOne({
        _id: leadId,
        isDeleted: false,
        $or: [
            { '_versioning.isLatest': true },
            { status: LeadStatus.Merged }, // allow deleting merged lead
        ],
    });

    if (!lead) {
        throw new InvalidInput({ leadId: 'Lead not found.' });
    }

    const hasPermission = permissionController.leads.mayOperateOn(lead, LeadPolicyAction.DeleteLead);

    if (!hasPermission) {
        throw new InvalidPermission();
    }

    // If deleting a main lead => suiteId of the main lead.
    const leadSuiteIds = [lead._versioning.suiteId];

    if (lead.status === LeadStatus.Merged && lead.mergedToLeadSuiteId) {
        // If deleting a merged lead => also include the suiteId of the main it was merged into.
        leadSuiteIds.push(lead.mergedToLeadSuiteId);
    }

    // find all leads with the same suite ID
    const leadIds = await collections.leads
        .find({
            isDeleted: false,
            $or: [
                { '_versioning.suiteId': { $in: leadSuiteIds } },
                {
                    // when delete main lead, also delete merged leads
                    status: LeadStatus.Merged,
                    mergedToLeadSuiteId: { $in: leadSuiteIds },
                },
            ],
        })
        .map(l => l._id)
        .toArray();

    // delete all leads with the same suite ID
    await collections.leads.updateMany(
        {
            _id: { $in: leadIds },
            isDeleted: false,
        },
        {
            $set: {
                isDeleted: true, // not update versioning because we may need revert deleted leads later
            },
        }
    );

    // delete applications related to this lead
    await collections.applications.updateMany(
        {
            leadId: { $in: leadIds },
            isDeleted: false,
        },
        {
            $set: {
                isDeleted: true, // not update versioning because we may need revert deleted applications later
            },
        }
    );

    // delete sales offers related to this lead
    await collections.salesOffers.updateMany(
        {
            leadSuiteId: lead._versioning.suiteId,
            isDeleted: false,
        },
        {
            $set: {
                isDeleted: true, // not update versioning because we may need revert deleted salesOffers later
            },
        }
    );

    return true;
};

export default requiresLoggedUser(resolver);
