import * as permissionKind from '../../../../../shared/permissions';
import { ModulePolicyAction } from '../../../../permissions';
import { createPermissionsResolver } from '../../../../utils/permissionResolvers';
import type { GraphQLPorscheIdModuleResolvers } from '../../definitions';

const resolver: GraphQLPorscheIdModuleResolvers = {
    id: root => root._id,
    company: (root, args, { loaders }) => loaders.companyById.load(root.companyId),
    versioning: root => root._versioning,
    permissions: (root, args, context) =>
        createPermissionsResolver(context, async () => {
            const { modules: controller } = await context.getPermissionController();

            return [
                [controller.mayOperateOn(root, ModulePolicyAction.Update), permissionKind.updateModule],
                [controller.mayOperateOn(root, ModulePolicyAction.Delete), permissionKind.deleteModule],
            ];
        }),
    porscheIdSetting: async (root, args, { loaders }) => {
        const porscheIdSetting = await loaders.porscheIdSettingByModuleId.load(root._id);

        return {
            ...porscheIdSetting,
            apiKey: porscheIdSetting?.secrets?.apiKey,
            audience: porscheIdSetting?.secrets?.audience,
            identityProvider: porscheIdSetting?.secrets?.identityProvider,
            userDataBaseUrl: porscheIdSetting?.secrets?.userDataBaseUrl,
        };
    },
};

export default resolver;
