import { extname } from 'path';
import type Excel from 'exceljs';
import type { TFunction } from 'i18next';
import { ofvRVTableMappedHeaders } from '../../../../../shared/constants';
import { ALLOWED_WORKBOOK_EXTS } from '../../../../utils/excel/constants';
import { getValue, getWorkbook, type MappedHeaders } from '../../../../utils/excel/utils';
import { allowedExtensions } from '../../../../utils/extensions';
import validateFile from '../../../../utils/validateFile';
import { requiresLoggedUser } from '../../../middlewares';
import type { GraphQLQueryResolvers } from '../../definitions';

const getOFVRVTableData = (
    sheet: Excel.Worksheet,
    mappedHeaders: MappedHeaders,
    t: TFunction<'translation', undefined>
) => {
    const data = [];
    const errors: string[] = [];

    const yearCols = sheet.getRow(1).values as string[];
    const modelCols = sheet.getColumn(1).values as string[];

    const duplicateModels = [
        ...new Set(
            modelCols.filter(
                (item, i, arr) => (typeof item === 'string' || typeof item === 'number') && arr.indexOf(item) !== i
            )
        ),
    ];

    const duplicateYears = [
        ...new Set(
            yearCols.filter(
                (item, i, arr) => (typeof item === 'string' || typeof item === 'number') && arr.indexOf(item) !== i
            )
        ),
    ];

    if (duplicateModels.length) {
        errors.push(t('ofrModuleDetails:rvTable.errors.duplicateModelsFound', { models: duplicateModels.join(', ') }));
    }

    if (duplicateYears.length) {
        errors.push(t('ofrModuleDetails:rvTable.errors.duplicateYearsFound', { years: duplicateYears.join(', ') }));
    }

    sheet.eachRow({ includeEmpty: true }, (row, rowNumber) => {
        const missingYearColumns: number[] = [];
        const missingModelRows: number[] = [];
        const invalidRValues: number[] = [];

        row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
            // break column number
            const breakColumnNumber = mappedHeaders.length - 1;

            // check if miss Year colum
            if (rowNumber === 1 && colNumber > breakColumnNumber && !Number.isFinite(cell.value)) {
                missingYearColumns.push(colNumber);
            }

            // check if miss Model colum
            if (colNumber <= breakColumnNumber && rowNumber > 1 && cell.value === null) {
                missingModelRows.push(rowNumber);
            } else if (rowNumber > 1 && colNumber > breakColumnNumber) {
                // check rv value cells
                if (typeof cell.value !== 'number' || !Number.isFinite(cell.value) || cell.value < 0) {
                    invalidRValues.push(colNumber);
                } else {
                    const otherProperty = mappedHeaders.reduce(
                        (acc, item, index) =>
                            index < breakColumnNumber
                                ? {
                                      ...acc,
                                      [item.key]: getValue(row.getCell(index + 1).text),
                                  }
                                : acc,
                        {}
                    );

                    const item = {
                        [mappedHeaders[breakColumnNumber].key]: getValue(sheet.getRow(1).getCell(colNumber).text),
                        value: getValue(cell.text),
                        ...otherProperty,
                    };
                    data.push(item);
                }
            }
        });

        if (missingYearColumns.length > 0) {
            errors.push(
                t('ofrModuleDetails:rvTable.errors.missingYearAtColumns', {
                    colNumbers: missingYearColumns.join(', '),
                })
            );
        }

        if (missingModelRows?.length > 0) {
            errors.push(
                t('ofrModuleDetails:rvTable.errors.missingModelAtRows', {
                    rowNumbers: missingModelRows.join(', '),
                })
            );
        }

        if (invalidRValues?.length > 0) {
            errors.push(
                t('ofrModuleDetails:rvTable.errors.numericFieldsMustBeGreaterThanOrEqualToZeroAtRow', {
                    rowNumber,
                    colNumbers: invalidRValues.join(', '),
                })
            );
        }
    });

    if (errors.length) {
        return { data: [], errors };
    }

    return { data, errors };
};

const query: GraphQLQueryResolvers['getImportedOFRRVTableFromExcel'] = async (root, { upload }, context) => {
    const { getTranslations } = context;
    const { t } = await getTranslations(['errors', 'ofrModuleDetails']);
    const acceptedExtensions = allowedExtensions.excel;
    const { filename, createReadStream } = await upload;
    const ext = extname(filename);
    const errors: string[] = [];

    if (!ALLOWED_WORKBOOK_EXTS.includes(ext.toLowerCase())) {
        errors.push(t('errors:unsupportedExtension'));
    }

    const isFileValid = await validateFile(createReadStream(), filename, acceptedExtensions);
    if (!isFileValid) {
        errors.push(t('errors:invalidFileFormat'));
    }

    if (errors?.length) {
        return { table: [], errors };
    }

    try {
        const workbook = await getWorkbook(createReadStream());
        const worksheet = workbook.getWorksheet(1);

        const { errors, data } = getOFVRVTableData(worksheet, ofvRVTableMappedHeaders as MappedHeaders, t);

        if (!data?.length) {
            errors.push(t('errors:noDataToImport'));
        }

        return { table: data, errors };
    } catch (error) {
        return { table: [], errors: [t('errors:anUnexpectedError', { error: error.message })] };
    }
};

export default requiresLoggedUser(query);
