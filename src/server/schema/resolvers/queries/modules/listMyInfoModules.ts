import type { Filter } from 'mongodb';
import { type Module, ModuleType } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { ensureMyInfoModulePage } from '../../../../integrations/myinfo';
import { paginateAggregation } from '../../../../utils/pagination';
import { requiresLoggedUser } from '../../../middlewares';
import type { GraphQLModuleFilteringRule, GraphQLQueryResolvers, Maybe } from '../../definitions';

const getFilter = (rule?: Maybe<GraphQLModuleFilteringRule>): Filter<Module> => {
    const filter: Filter<Module> = { _type: ModuleType.MyInfoModule };

    if (!rule) {
        return filter;
    }

    if (rule.companyId) {
        filter.companyId = rule.companyId;
    }

    return filter;
};

const query: GraphQLQueryResolvers['listMyInfoModules'] = async (root, { pagination, filter }) => {
    const { collections } = await getDatabaseContext();

    return paginateAggregation(
        collections.modules,
        [
            {
                $match: {
                    $and: [getFilter(filter)],
                },
            },
            { $sort: { _id: 1 } },
        ],
        pagination
    ).then(ensureMyInfoModulePage(ModuleType.MyInfoModule));
};

export default requiresLoggedUser(query);
