import { ObjectId } from 'mongodb';
import { ApplicationStage, ApplicationStatus } from '../../../../database/documents/Applications/core';
import { applicationModuleTypes } from '../../../../database/documents/modules';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { STAGE_TO_STATUS_MAPPING } from '../../../../utils/application';
import type { GraphQLQueryResolvers } from '../../definitions';

const query: GraphQLQueryResolvers['getApplicationsListFilters'] = async (_root, { filter }) => {
    const { collections } = await getDatabaseContext();

    const applicationStatus = filter?.stage ? STAGE_TO_STATUS_MAPPING[filter.stage] || [] : [];

    const companies = await collections.companies
        .find({
            ...(filter.companyIds?.length > 0 && {
                _id: { $in: filter.companyIds.map(companyId => new ObjectId(companyId)) },
            }),
        })
        .toArray();

    const modules = await collections.modules
        .find({
            companyId: { $in: companies.map(company => company._id) },
            _type: { $in: applicationModuleTypes },
            ...(filter.moduleIds?.length > 0 && {
                _id: { $in: filter.moduleIds.map(moduleId => new ObjectId(moduleId)) },
            }),
        })
        .toArray();

    return {
        modules,
        companies,
        applicationStatus:
            filter.stage === ApplicationStage.Appointment
                ? applicationStatus.filter(status => status !== ApplicationStatus.Completed)
                : applicationStatus,
    };
};

export default query;
