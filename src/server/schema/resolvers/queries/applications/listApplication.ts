import dayjs from 'dayjs';
import { camelCase, isNil } from 'lodash/fp';
import type { Document, Filter, Sort } from 'mongodb';
import type { Application } from '../../../../database/documents';
import {
    ApplicationKind,
    ApplicationStage,
    MobilityBookingLocationType,
    ModuleType,
} from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { getPathByStage } from '../../../../utils/application';
import { getSortingValue, paginateAggregation } from '../../../../utils/pagination';
import { requiresLoggedUser } from '../../../middlewares';
import type {
    GraphQLApplicationFilteringRule,
    GraphQLApplicationSortingRule,
    GraphQLQueryResolvers,
    Maybe,
} from '../../definitions';
import { ApplicationSortingField } from '../../definitions';
import fullNameReduce from '../customers/fullNameReduce';

export const getFilter = (rule?: Maybe<GraphQLApplicationFilteringRule>): Filter<Application> => {
    const filter: Filter<Application> = {
        '_versioning.isLatest': true,
        isDraft: false,
    };

    if (!rule) {
        return filter;
    }

    const andFilter: Filter<Application>['$and'] = [];

    if (rule.bank) {
        filter['bank.displayName'] = new RegExp(rule.bank, 'i');
    }

    if (rule.financeProduct) {
        filter['financeProduct.displayName'] = new RegExp(rule.financeProduct, 'i');
    }

    if (rule.vehicle) {
        filter['vehicle.name.defaultValue'] = new RegExp(rule.vehicle, 'i');
    }

    if (rule.moduleIds?.length) {
        filter.moduleId = { $in: rule.moduleIds };
    }

    if (rule.eventId) {
        filter.eventId = rule.eventId;
    }

    if (rule.stage) {
        filter.stages = rule.stage;
        const path = getPathByStage(rule.stage);

        if (path && rule.status) {
            filter[`${path}.status`] = { $in: rule.status };
        }

        if (path && rule.identifier) {
            filter[`${path}.identifier`] = new RegExp(rule.identifier, 'i');
        }

        // filter application that isDraft of stage info not exist
        // or exist but value is false
        if (
            [
                ApplicationStage.Financing,
                ApplicationStage.Insurance,
                ApplicationStage.VisitAppointment,
                ApplicationStage.Appointment,
            ].includes(rule.stage)
        ) {
            andFilter.push({
                $or: [
                    { [`${path}.isDraft`]: { $exists: false } },
                    { [`${path}.isDraft`]: { $exists: true }, [`${path}.isDraft`]: false },
                ],
            });
        }
    }

    let foundKind: ApplicationKind | undefined;
    if (rule.kind) {
        const kind = rule.kind.toLowerCase();

        switch (kind) {
            case ApplicationKind.Standard:
            case ApplicationKind.Event:
            case ApplicationKind.Configurator:
            case ApplicationKind.Mobility:
            case ApplicationKind.Finder:
            case ApplicationKind.Launchpad:
                filter.kind = kind;
                foundKind = kind;
        }
    }
    if (rule.dealerIds?.length > 0) {
        andFilter.push({
            $or: [
                { dealerId: { $in: rule.dealerIds } },
                {
                    kind: {
                        $in: [foundKind].filter(Boolean),
                    },
                },
            ],
        });
    }

    if (rule.applicantIds) {
        filter.applicantId = { $in: rule.applicantIds };
    }

    if (rule.dealerName) {
        filter['dealer.displayName'] = new RegExp(rule.dealerName, 'i');
    }

    if (rule.moduleName) {
        filter['module.displayName'] = new RegExp(rule.moduleName, 'i');
    }

    if (rule.companyIds?.length) {
        filter['module.companyId'] = { $in: rule.companyIds };
    }

    if (rule.transactionId) {
        filter['applicationJourney.deposit.transactionId'] = new RegExp(rule.transactionId, 'i');
    }

    if (rule.assignee) {
        filter['assignee.displayName'] = new RegExp(rule.assignee, 'i');
    }

    if (rule.inventoryStockId) {
        filter['mobilityBookingDetails.inventoryStockId'] = rule.inventoryStockId;
    }

    if (rule.configuratorId) {
        filter['configurator.modelConfiguratorId'] = rule.configuratorId;
    }

    if (rule.locations) {
        andFilter.push({
            $or: [
                rule.locations.includes('homeDelivery') && {
                    'mobilityBookingDetails.location._type': MobilityBookingLocationType.Home,
                },
                {
                    'mobilityBookingDetails.location._type': MobilityBookingLocationType.Pickup,
                    'mobilityBookingDetails.location.name': { $in: rule.locations },
                },
            ].filter(Boolean),
        });
    }

    if (rule.finderVehicleVin) {
        filter['vehicle.listing.vehicle.vin'] = new RegExp(rule.finderVehicleVin, 'i');
    }

    if (rule.customer) {
        filter['customer.fullName'] = new RegExp(rule.customer, 'i');
    }

    if (rule.startDateRange) {
        const { start, end } = rule.startDateRange;
        const startOfDay = dayjs(start).startOf('day').toDate();
        const endOfDay = dayjs(end).endOf('day').toDate();
        filter['mobilityBookingDetails.period.start'] = { $gte: startOfDay, $lte: endOfDay };
    }

    if (rule.endDateRange) {
        const { start, end } = rule.endDateRange;
        const startOfDay = dayjs(start).startOf('day').toDate();
        const endOfDay = dayjs(end).endOf('day').toDate();
        filter['mobilityBookingDetails.period.end'] = { $gte: startOfDay, $lte: endOfDay };
    }

    if (rule.createdDateRange) {
        const { start, end } = rule.createdDateRange;
        const startOfDay = dayjs(start).startOf('day').toDate();
        const endOfDay = dayjs(end).endOf('day').toDate();
        filter['_versioning.createdAt'] = { $gte: startOfDay, $lte: endOfDay };
    }

    if (rule.appointmentDateRange) {
        const { start, end } = rule.appointmentDateRange;
        const startOfDay = dayjs(start).startOf('day').toDate();
        const endOfDay = dayjs(end).endOf('day').toDate();

        if (rule.stage === ApplicationStage.FollowUp) {
            filter['followUpStage.scheduledDate'] = { $gte: startOfDay, $lte: endOfDay };
        } else {
            filter['appointmentStage.bookingTimeSlot.slot'] = { $gte: startOfDay, $lte: endOfDay };
        }
    }

    if (rule.vin) {
        filter['inventory.stocks.vin'] = rule.vin;
    }

    if (rule.insurer) {
        filter['insurer.displayName'] = new RegExp(rule.insurer, 'i');
    }

    if (rule.eventDisplayName) {
        filter['event.displayName'] = new RegExp(rule.eventDisplayName, 'i');
    }

    if (rule.eventCampaignId) {
        filter['campaignValues.capCampaignId'] = new RegExp(rule.eventCampaignId, 'i');
    }

    if (andFilter.length > 0) {
        filter.$and = andFilter;
    }

    if (rule.appointmentModuleFilter) {
        if (rule.appointmentModuleFilter === ModuleType.VisitAppointmentModule) {
            filter['visitAppointmentModule._type'] = camelCase(rule.appointmentModuleFilter);
        }

        if (rule.appointmentModuleFilter === ModuleType.AppointmentModule) {
            filter['appointmentModule._type'] = camelCase(rule.appointmentModuleFilter);
        }
    }

    return filter;
};

const getSort = (
    rule?: Maybe<GraphQLApplicationSortingRule>,
    filter?: Maybe<GraphQLApplicationFilteringRule>
): Sort => {
    const sort: Sort = { _id: 1 };

    if (!rule) {
        return sort;
    }

    switch (rule.field) {
        case ApplicationSortingField.ApplicationStatus: {
            switch (filter?.stage) {
                case ApplicationStage.Appointment:
                    return { 'appointmentStage.status': getSortingValue(rule.order), ...sort };

                case ApplicationStage.Insurance:
                    return { 'insuranceStage.status': getSortingValue(rule.order), ...sort };

                case ApplicationStage.Financing:
                    return { 'financingStage.status': getSortingValue(rule.order), ...sort };

                case ApplicationStage.Mobility:
                    return { 'mobilityStage.status': getSortingValue(rule.order), ...sort };

                case ApplicationStage.Reservation:
                    return { 'reservationStage.status': getSortingValue(rule.order), ...sort };

                case ApplicationStage.VisitAppointment:
                    return { 'visitAppointmentStage.status': getSortingValue(rule.order), ...sort };

                case ApplicationStage.TradeIn:
                    return { 'tradeInStage.status': getSortingValue(rule.order), ...sort };

                case ApplicationStage.FollowUp:
                    return { 'followUpStage.status': getSortingValue(rule.order), ...sort };

                default:
                    throw new Error('ApplicationStage not supported for stage');
            }
        }

        case ApplicationSortingField.Bank:
            return { 'bank.displayName': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.Company:
            return { 'module.company.displayName': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.FinanceProduct:
            return { 'financeProduct.displayName': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.Identifier: {
            switch (filter?.stage) {
                case ApplicationStage.Appointment:
                    return { 'appointmentStage.identifier': getSortingValue(rule.order), ...sort };

                case ApplicationStage.Insurance:
                    return { 'insuranceStage.identifier': getSortingValue(rule.order), ...sort };

                case ApplicationStage.Financing:
                    return { 'financingStage.identifier': getSortingValue(rule.order), ...sort };

                case ApplicationStage.Mobility:
                    return { 'mobilityStage.identifier': getSortingValue(rule.order), ...sort };

                case ApplicationStage.Reservation:
                    return { 'reservationStage.identifier': getSortingValue(rule.order), ...sort };

                case ApplicationStage.FollowUp:
                    return { 'followUpStage.identifier': getSortingValue(rule.order), ...sort };

                default:
                    throw new Error('ApplicationStage not supported for identifier');
            }
        }

        case ApplicationSortingField.Vehicle:
            return { 'vehicle.identifier': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.ApplicationDate:
            return { '_versioning.createdAt': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.Assignee:
            return { 'assignee.displayName': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.TransactionId:
            return { 'applicationJourney.deposit.transactionId': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.LastActivity:
            return { '_versioning.updatedAt': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.Module:
            return { 'module.displayName': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.BookingStartDate:
            return { 'mobilityBookingDetails.period.start': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.BookingEndDate:
            return { 'mobilityBookingDetails.period.end': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.BookingSubmitted:
            return { '_versioning.createdAt': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.VehicleName:
            return { 'vehicle.name.defaultValue': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.BookingLocation:
            return { 'mobilityBookingDetails.location.name': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.Customer:
            return { 'customer.fullName': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.TotalAmountPaid:
            return { 'applicationJourney.deposit.amount': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.AppointmentDate:
            if (filter?.stage === ApplicationStage.FollowUp) {
                return { 'followUpStage.scheduledDate': getSortingValue(rule.order), ...sort };
            }

            return { 'appointmentStage.bookingTimeSlot.slot': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.Insurer:
            return { 'insurer.displayName': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.InsurancePremium:
            return { 'insurancing.insurancePremium': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.FinderVin:
            return { 'vehicle.listing.vehicle.vin': getSortingValue(rule.order), ...sort };

        case ApplicationSortingField.CapStatus:
            return { 'capValues.status': getSortingValue(rule.order), ...sort };

        default:
            throw new Error('Sorting Field not supported');
    }
};

const getSearchKeyword = (keyword: string) => {
    if (!keyword) {
        return null;
    }
    const value = new RegExp(keyword, 'i');

    return {
        $or: [
            {
                'customer.fields.deterministicString': value,
                'customer.fields.key': 'fullName',
            },
            {
                'customer.fields.deterministicString': value,
                'customer.fields.key': 'firstName',
            },
            {
                'customer.fields.deterministicString': value,
                'customer.fields.key': 'lastName',
            },
            {
                'customer.fields.deterministicString': value,
                'customer.fields.key': 'email',
            },
            {
                'customer.fields.deterministicString': value,
                'customer.fields.key': 'identityNumber',
            },
            {
                'customer.fields.randomizedPhone': value,
                'customer.fields.key': 'phone',
            },
            { 'assignee.displayName': value },
            { identifier: value },
            { 'vehicle.name.defaultValue': value },
        ],
    };
};

const getDateList = (rule?: Maybe<GraphQLApplicationFilteringRule>) => {
    const date = new Date();
    let firstDate;
    let lastDate;

    if (!rule?.lastMonth && !rule?.currentMonth) {
        return null;
    }

    if (rule?.currentMonth) {
        firstDate = new Date(date.getFullYear(), date.getMonth(), 1);
        lastDate = new Date(date.getFullYear(), date.getMonth() + 1, 1);
    }

    if (rule?.lastMonth) {
        firstDate = new Date(date.getFullYear(), date.getMonth() - 1, 1);
        lastDate = new Date(date.getFullYear(), date.getMonth(), 1);
    }

    return {
        '_versioning.createdAt': { $lte: dayjs(lastDate).toDate(), $gt: dayjs(firstDate).toDate() },
    };
};

const lookUpModulesPipelines = [
    {
        $lookup: {
            from: 'modules',
            localField: 'moduleId',
            foreignField: '_id',
            as: 'module',
        },
    },
    {
        $unwind: {
            path: '$module',
            preserveNullAndEmptyArrays: false,
        },
    },
];

const lookupAppointmentModulePipeline = [
    {
        $lookup: {
            from: 'modules',
            localField: 'moduleId',
            foreignField: '_id',
            as: 'module',
        },
    },
    {
        $lookup: {
            from: 'modules',
            localField: 'module.appointmentModuleId',
            foreignField: '_id',
            as: 'appointmentModule',
        },
    },
    {
        $unwind: {
            path: '$appointmentModule',
            preserveNullAndEmptyArrays: false,
        },
    },
];

const lookupVisitAppointmentModulePipeline = [
    {
        $lookup: {
            from: 'modules',
            localField: 'moduleId',
            foreignField: '_id',
            as: 'module',
        },
    },
    {
        $lookup: {
            from: 'modules',
            localField: 'module.visitAppointmentModuleId',
            foreignField: '_id',
            as: 'visitAppointmentModule',
        },
    },
    {
        $unwind: {
            path: '$visitAppointmentModule',
            preserveNullAndEmptyArrays: false,
        },
    },
];

const lookupLeadPipelines = [
    {
        $lookup: {
            from: 'leads',
            localField: 'leadId',
            foreignField: '_id',
            as: 'lead',
        },
    },
    {
        $unwind: {
            path: '$lead',
            preserveNullAndEmptyArrays: false,
        },
    },
];

const lookUpCompaniesPipelines = [
    {
        $lookup: {
            from: 'companies',
            localField: 'module.companyId',
            foreignField: '_id',
            as: 'module.company',
        },
    },
    {
        $unwind: {
            path: '$module.company',
            preserveNullAndEmptyArrays: false,
        },
    },
];

const lookUpDealerPipelines = [
    {
        $lookup: {
            from: 'dealers',
            localField: 'dealerId',
            foreignField: '_id',
            as: 'dealer',
        },
    },
    {
        $unwind: {
            path: '$dealer',
            preserveNullAndEmptyArrays: false,
        },
    },
];

const lookUpCustomerPipelines = [
    {
        $lookup: {
            from: 'customers',
            localField: 'applicantId',
            foreignField: '_id',
            as: 'customer',
        },
    },
    {
        $unwind: {
            path: '$customer',
            preserveNullAndEmptyArrays: false,
        },
    },
    { $addFields: { 'customer.fullName': fullNameReduce } },
];

const getLookUpUserPipelines = (filter: Maybe<GraphQLApplicationFilteringRule>) => {
    const stagePath = getPathByStage(filter.stage);
    if (isNil(stagePath)) {
        return [];
    }

    return [
        {
            $lookup: {
                from: 'users',
                localField: `${stagePath}.assigneeId`,
                foreignField: '_id',
                as: 'assignee',
            },
        },
        {
            $unwind: {
                path: '$assignee',
                preserveNullAndEmptyArrays: true,
            },
        },
    ];
};

// look up financeProducts document
const lookUpFinanceProductPipelines = [
    {
        $lookup: {
            from: 'financeProducts',
            localField: 'financing.financeProductId',
            foreignField: '_id',
            as: 'financeProduct',
        },
    },
    {
        $unwind: {
            path: '$financeProduct',
            preserveNullAndEmptyArrays: false,
        },
    },
];

// look up banks document
const lookUpBankPipelines = [
    {
        $lookup: {
            from: 'banks',
            localField: 'bankId',
            foreignField: '_id',
            as: 'bank',
        },
    },
    {
        $unwind: {
            path: '$bank',
            preserveNullAndEmptyArrays: false,
        },
    },
];

// look up vehicles documents
const lookUpVehiclePipelines = [
    {
        $lookup: {
            from: 'vehicles',
            localField: 'vehicleId',
            foreignField: '_id',
            as: 'vehicle',
        },
    },
    {
        $unwind: {
            path: '$vehicle',
            preserveNullAndEmptyArrays: false,
        },
    },
];

// look up configurator documents
export const lookUpConfiguratorPipelines = [
    {
        $lookup: {
            from: 'configurators',
            localField: 'configuratorId',
            foreignField: '_id',
            as: 'configurator',
        },
    },
    {
        $unwind: {
            path: '$configurator',
            preserveNullAndEmptyArrays: false,
        },
    },
];

export const lookUpInventoryPipelines = [
    {
        $lookup: {
            from: 'inventories',
            localField: '_versioning.suiteId',
            foreignField: 'stocks.reservations.applicationId',
            as: 'inventory',
        },
    },
    {
        $unwind: {
            path: '$inventory',
            preserveNullAndEmptyArrays: false,
        },
    },
];

// look up application journey documents
export const lookUpApplicationJourneyPipelines = [
    {
        $lookup: {
            from: 'applicationJourneys',
            localField: '_versioning.suiteId',
            foreignField: 'applicationSuiteId',
            as: 'applicationJourney',
        },
    },
    {
        $unwind: {
            path: '$applicationJourney',
            preserveNullAndEmptyArrays: false,
        },
    },
];

const lookUpInsurerPipelines = [
    {
        $lookup: {
            from: 'insurers',
            localField: 'insurancing.insurerId',
            foreignField: '_id',
            as: 'insurer',
        },
    },
    {
        $unwind: {
            path: '$insurer',
            preserveNullAndEmptyArrays: false,
        },
    },
];

const lookupEventPipelines = [
    {
        $lookup: {
            from: 'events',
            localField: 'eventId',
            foreignField: '_id',
            as: 'event',
        },
    },
    {
        $unwind: {
            path: '$event',
            preserveNullAndEmptyArrays: false,
        },
    },
];

export const pipelineHandler = (
    sort: GraphQLApplicationSortingRule,
    filter: GraphQLApplicationFilteringRule,
    options?: { forceLookupModulesAndCompanies?: boolean }
) => {
    const pipeline: Document[] = [...lookupLeadPipelines];

    if (filter?.financeProduct || sort?.field === ApplicationSortingField.FinanceProduct) {
        pipeline.push(...lookUpFinanceProductPipelines);
    }

    if (filter?.bank || sort?.field === ApplicationSortingField.Bank) {
        pipeline.push(...lookUpBankPipelines);
    }

    if (
        filter?.keyword ||
        filter?.vehicle ||
        filter?.vehicleNames ||
        filter?.finderVehicleVin ||
        sort?.field === ApplicationSortingField.Vehicle ||
        sort?.field === ApplicationSortingField.VehicleName
    ) {
        pipeline.push(...lookUpVehiclePipelines);
    }

    if (filter?.dealerName) {
        pipeline.push(...lookUpDealerPipelines);
    }

    if (
        filter?.moduleName ||
        filter?.companyIds ||
        sort?.field === ApplicationSortingField.Module ||
        sort?.field === ApplicationSortingField.Company ||
        options?.forceLookupModulesAndCompanies
    ) {
        pipeline.push(...lookUpModulesPipelines);
        if (
            filter?.companyIds ||
            sort?.field === ApplicationSortingField.Company ||
            options?.forceLookupModulesAndCompanies
        ) {
            pipeline.push(...lookUpCompaniesPipelines);
        }
    }

    if (filter?.keyword || filter?.assignee || sort?.field === ApplicationSortingField.Assignee) {
        pipeline.push(...getLookUpUserPipelines(filter));
    }

    if (filter?.keyword || filter?.customer || sort?.field === ApplicationSortingField.Customer) {
        pipeline.push(...lookUpCustomerPipelines);
    }

    if (filter?.configuratorId) {
        pipeline.push(...lookUpConfiguratorPipelines);
    }

    if (filter?.vin) {
        pipeline.push(...lookUpInventoryPipelines);
    }

    if (
        filter?.transactionId ||
        sort?.field === ApplicationSortingField.TransactionId ||
        sort?.field === ApplicationSortingField.TotalAmountPaid
    ) {
        pipeline.push(...lookUpApplicationJourneyPipelines);
    }

    if (filter?.insurer || sort?.field === ApplicationSortingField.Insurer) {
        pipeline.push(...lookUpInsurerPipelines);
    }

    if (filter?.eventDisplayName || filter?.eventCampaignId) {
        pipeline.push(...lookupEventPipelines);
    }

    if (filter?.appointmentModuleFilter) {
        const typeOfAppointmentPipeline =
            filter?.appointmentModuleFilter === ModuleType.AppointmentModule
                ? lookupAppointmentModulePipeline
                : lookupVisitAppointmentModulePipeline;
        pipeline.push(...typeOfAppointmentPipeline);
    }

    pipeline.push(
        {
            $match: {
                ...getFilter(filter),
                ...getSearchKeyword(filter?.keyword),
                ...getDateList(filter),
            },
        },
        { $sort: getSort(sort, filter) }
    );

    return pipeline;
};

const query: GraphQLQueryResolvers['listApplications'] = async (
    root,
    { pagination, sort, filter },
    { getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const permissions = await getPermissionController();

    const permissionFilter = await permissions.applications.getFilterQueryForStage(filter?.stage);

    const pipeline: Document[] = [{ $match: permissionFilter }, ...pipelineHandler(sort, filter)];

    return paginateAggregation(collections.applications, pipeline, pagination);
};

export default requiresLoggedUser(query);
