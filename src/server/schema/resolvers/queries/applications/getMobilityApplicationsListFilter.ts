import { uniqBy } from 'lodash/fp';
import type { Company, Module, MobilityBookingLocation } from '../../../../database/documents';
import { ApplicationStage, MobilityBookingLocationType } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { STAGE_TO_STATUS_MAPPING } from '../../../../utils/application';
import type { GraphQLQueryResolvers } from '../../definitions';
import { pipelineHandler } from './listApplication';

type MobilityFilterAggregation = {
    modules: Module[];
    companies: Company[];
    mobilityLocations: MobilityBookingLocation[];
};

const query: GraphQLQueryResolvers['getMobilityApplicationsListFilters'] = async (
    _root,
    { filter },
    { getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const permissions = await getPermissionController();
    const permissionFilter = await permissions.applications.getFilterQueryForStage(ApplicationStage.Mobility);

    const applicationStatus = STAGE_TO_STATUS_MAPPING[ApplicationStage.Mobility] || [];

    const applications = (
        await collections.applications
            .aggregate<MobilityFilterAggregation>([
                { $match: permissionFilter },
                ...pipelineHandler(
                    null,
                    { ...filter, stage: ApplicationStage.Mobility },
                    {
                        forceLookupModulesAndCompanies: true,
                    }
                ),
                {
                    $group: {
                        _id: null,
                        modules: { $addToSet: '$module' },
                        companies: { $addToSet: '$module.company' },
                        mobilityLocations: { $addToSet: '$mobilityBookingDetails.location' },
                    },
                },
                { $project: { _id: 0 } },
            ])
            .toArray()
    )[0];

    const mobilityLocations =
        applications?.mobilityLocations?.length > 0
            ? uniqBy(location => {
                  if (location._type === MobilityBookingLocationType.Home) {
                      return location._type;
                  }

                  return location.name;
              }, applications.mobilityLocations)
            : [];

    return {
        applicationStatus,
        modules: applications ? uniqBy(module => module._id.toString(), applications.modules) : [],
        companies: applications ? uniqBy(company => company._id.toString(), applications.companies) : [],
        mobilityLocations,
    };
};

export default query;
