import { flatten, isEmpty, uniqBy } from 'lodash/fp';
import type { ObjectId } from 'mongodb';
import { AuditTrailKind } from '../../../../database/documents/AuditTrail';
import { getLocalCustomerAggregatedFields } from '../../../../database/helpers/customers/shared';
import { ppnAuth, searchBusinessPartner } from '../../../../integrations/cap';
// eslint-disable-next-line max-len
import getBusinessPartnerByMultipleMobileFormat from '../../../../integrations/cap/businessPartner/getBusinessPartnerByMultipleMobileFormat';
import type { BusinessPartnerResData } from '../../../../integrations/cap/businessPartner/types';
import { getCustomerCompetitorVehicleDetails } from '../../../../integrations/cap/customerCompetitorVehicle';
import { createCapSubmissionAuditTrails, getCustomerCurrentVehicleFromCap } from '../../../../integrations/cap/utils';
// eslint-disable-next-line max-len
import formatBpCompetitorVehicleReturnValue from '../../../../integrations/cap/utils/formatBpCompetitorVehicleReturnValue';
import formatBusinessPartnerReturnValue from '../../../../integrations/cap/utils/formatBusinessPartnerReturnValue';
import { getCustomerExistingHobbies } from '../../../../integrations/cap/utils/getExistingHobbies';
import { type GenericGetResult } from '../../../../integrations/cap/utils/types';
import { LeadPolicyAction } from '../../../../permissions/types/leads';
import { ModulePolicyAction } from '../../../../permissions/types/modules';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import {
    type GraphQLCapBusinessPartnerData,
    type GraphQLGetCapBusinessPartnerQueryInput,
    type GraphQLQueryResolvers,
    ModuleType,
} from '../../definitions';
import { BusinessPartnerSearchField } from '../../enums';

const validateReferenceValues = (
    leadId: ObjectId | null,
    query: GraphQLGetCapBusinessPartnerQueryInput | null,
    searchFields: BusinessPartnerSearchField[]
) => {
    if (
        !leadId &&
        (!query || Object.keys(query).length === 0 || Object.values(query || {}).every(value => isEmpty(value)))
    ) {
        throw new InvalidInput({ ref: 'Reference value is invalid' });
    }

    if (!searchFields || searchFields.length === 0) {
        throw new InvalidInput({ searchFields: 'Search fields are required' });
    }
};

/* 
    Note : If the query has leadId, meaning the request coming from lead details
*/

/* 
    Some functions is commented and will reverted to using thee old functions since there're some edge cases from C@P
    where the enhanced function for getting BP in one query with pagination is getting error from C@P.

    DO NOT REMOVE THESE COMMENTED FUNCTIONS
*/

const query: GraphQLQueryResolvers['getCapBusinessPartners'] = async (
    root,
    { applicationModuleId, eventId, capModuleId, leadId, dealerId, query, searchFields },
    { loaders, getTranslations, getPermissionController }
) => {
    const { leads: leadPermission, modules: modulePermission } = await getPermissionController();
    const { t } = await getTranslations(['auditTrails']);

    validateReferenceValues(leadId, query, searchFields);

    const applicationModule = await loaders.moduleById.load(applicationModuleId);

    if (!applicationModule) {
        throw new InvalidInput({ module: `Module of this application is invalid.` });
    }

    const company = await loaders.companyById.load(applicationModule.companyId);
    if (!company) {
        throw new InvalidInput({ module: `Company of this application is invalid.` });
    }

    // User access check
    const hasLeadUpdate = !!leadId && leadPermission.hasPolicyForAction(LeadPolicyAction.UpdateContact);
    const hasModuleCreate =
        modulePermission.mayOperateOn(applicationModule, ModulePolicyAction.CreateContact) ||
        modulePermission.mayOperateOn(applicationModule, ModulePolicyAction.CreateApplication);

    if (!hasLeadUpdate && !hasModuleCreate) {
        throw new InvalidPermission();
    }

    if (applicationModule._type === ModuleType.EventApplicationModule) {
        if (!eventId) {
            throw new InvalidInput({ event: `Invalid Event ID` });
        }

        const eventDetails = await loaders.eventById.load(eventId);
        if (!eventDetails.isCapEnabled) {
            throw new InvalidInput({ event: `This event does not enable C@P integrated` });
        }
    } else if (
        applicationModule._type === ModuleType.ConfiguratorModule ||
        applicationModule._type === ModuleType.FinderApplicationPrivateModule ||
        applicationModule._type === ModuleType.FinderApplicationPublicModule ||
        applicationModule._type === ModuleType.LaunchPadModule ||
        applicationModule._type === ModuleType.StandardApplicationModule
    ) {
        if (!applicationModule.capModuleId) {
            throw new InvalidInput({ module: `This module does not have C@P module integrated` });
        }
    } else {
        throw new InvalidInput({ module: `Module of this application is invalid.` });
    }

    const capSetting = await loaders.capSettingByModuleId.load(capModuleId);

    const lead = leadId ? await loaders.leadById.load(leadId) : null;

    const authentication = await ppnAuth(capSetting);
    if (authentication.error) {
        const errorMessage = t('auditTrails:application.defaultCapError.authFailed');
        if (lead) {
            await createCapSubmissionAuditTrails({
                lead,
                capActionAuditTrail: AuditTrailKind.CapBPSearchFailed,
                success: false,
                errorMessage,
            });
        }
        throw new Error(errorMessage);
    }

    const getCustomerData = async () => {
        if (lead) {
            const [customerData, dealerData] = await Promise.all([
                loaders.customerById.load(lead.customerId),
                loaders.dealerById.load(lead.dealerId),
            ]);
            const customerFields = getLocalCustomerAggregatedFields(customerData);

            return {
                email: customerFields.email ?? '',
                mobileNumber: {
                    prefix: customerFields.phone?.prefix ?? '',
                    value: customerFields.phone?.value ?? '',
                },
                firstName: customerFields.firstName ?? '',
                lastName: customerFields.lastName ?? '',
                firstNameJapan: customerFields.firstNameJapan ?? '',
                lastNameJapan: customerFields.lastNameJapan ?? '',
                vin: customerFields.currentVehicleVin ?? lead.tradeInVehicle?.[0]?.vin ?? '',

                dealer: dealerData.integrationDetails?.dealerCode ?? '',
            };
        }

        const dealerData = await loaders.dealerById.load(dealerId);

        return {
            email: query?.email ?? '',
            mobileNumber: {
                prefix: null, // Assuming the prefix is unclear from the user input
                value: query?.phone ?? '',
            },
            firstName: query?.firstName ?? '',
            lastName: query?.lastName ?? '',
            firstNameJapan: query?.firstNameJapan ?? '',
            lastNameJapan: query?.lastNameJapan ?? '',
            vin: query?.vin ?? '',
            dealer: dealerData.integrationDetails?.dealerCode ?? '',
        };
    };

    const customerData = await getCustomerData();

    if (isEmpty(customerData.dealer)) {
        return { count: 0, items: [] };
    }

    // Start of old function
    const searchQueries = (() => {
        const searchFieldMap = new Map([
            [BusinessPartnerSearchField.Email, { queryKeys: ['email'], dataKeys: ['email'] }],
            [BusinessPartnerSearchField.Phone, { queryKeys: ['mobileNumber'], dataKeys: ['mobileNumber'] }],
            [BusinessPartnerSearchField.Vin, { queryKeys: ['vin'], dataKeys: ['vin'] }],
            [BusinessPartnerSearchField.PorscheId, { queryKeys: ['porscheId'], dataKeys: ['email'] }],
            [
                BusinessPartnerSearchField.FirstAndLastName,
                { queryKeys: ['firstName', 'lastName'], dataKeys: ['firstName', 'lastName'] },
            ],
            [
                BusinessPartnerSearchField.FirstAndLastNameJapan,
                { queryKeys: ['firstNameJapan', 'lastNameJapan'], dataKeys: ['firstNameJapan', 'lastNameJapan'] },
            ],
        ]);

        const queries = [];

        for (const field of searchFields) {
            const fieldConfig = searchFieldMap.get(field);

            if (fieldConfig && fieldConfig.dataKeys.some(key => !isEmpty(customerData?.[key] ?? ''))) {
                const filterArr = fieldConfig.queryKeys
                    .map((queryKey, index) => [queryKey, customerData?.[fieldConfig.dataKeys[index]] ?? ''])
                    .filter(([key, value]) => !isEmpty(value));

                const searchQuery = {
                    ...Object.fromEntries(filterArr),
                    dealer: customerData.dealer,
                    size: 50,
                };

                if (field === BusinessPartnerSearchField.Phone) {
                    queries.push(
                        getBusinessPartnerByMultipleMobileFormat({
                            ...capSetting,
                            lead,
                            t,
                            auth: authentication.access_token,
                            phone: searchQuery.mobileNumber.value,
                            prefix: searchQuery.mobileNumber.prefix,
                            companyCountryCode: company.countryCode,
                            dealer: searchQuery.dealer,
                            size: searchQuery.size,
                        })
                    );
                } else {
                    queries.push(
                        searchBusinessPartner({
                            ...capSetting,
                            lead,
                            t,
                            auth: authentication.access_token,
                            query: searchQuery,
                        })
                    );
                }
            }
        }

        return queries;
    })();

    const businessPartnerDetailData: GenericGetResult<BusinessPartnerResData>[] = await Promise.all(
        searchQueries.filter(Boolean)
    );

    const combinedBusinessPartnerData: {
        data?: GraphQLCapBusinessPartnerData;
        error?: string;
    }[] = flatten(
        await Promise.all(
            businessPartnerDetailData
                .map(async bpDetail => {
                    if (bpDetail?.error) {
                        return {
                            data: null,
                            error: bpDetail.error,
                        };
                    }

                    if (!bpDetail?.d?.results?.length) {
                        return null;
                    }

                    const {
                        d: { results },
                    } = bpDetail;

                    const resultPromises = results.map(async result => ({
                        data: await formatBusinessPartnerReturnValue(result, company, loaders),
                        error: null,
                    }));

                    return Promise.all(resultPromises);
                })
                .filter(Boolean)
        )
    ).filter(Boolean);

    // If all of the query is error
    if (combinedBusinessPartnerData.length && combinedBusinessPartnerData.every(bpData => !isEmpty(bpData?.error))) {
        const uniqError = uniqBy('error', combinedBusinessPartnerData)[0].error;

        if (lead) {
            await createCapSubmissionAuditTrails({
                lead,
                capActionAuditTrail: AuditTrailKind.CapBPSearchFailed,
                success: false,
                errorMessage: uniqError,
            });
        }
    }

    const combinedBpData = combinedBusinessPartnerData.map(bp => bp.data).filter(Boolean);

    const uniqueBusinessPartners = uniqBy('businessPartnerGuid', combinedBpData);

    const businessPartnerIds = uniqueBusinessPartners.map(bp => bp.businessPartnerId);
    // End of old function

    // DO NOT REMOVE THIS
    /* 
    const businessPartnerDetailData: GenericGetResult<BusinessPartnerResData> = await searchBusinessPartner({
        ...capSetting,
        t,
        auth: authentication.access_token,
        query: {
            page: Math.floor(pagination.offset / pagination.limit) + 1,
            size: pagination.limit,
            email: customerData.email,
            mobileNumber: customerData.mobileNumber,
            dealer: customerData.dealer,
        },
    });

    if (businessPartnerDetailData?.error) {
        await createCapSubmissionAuditTrails({
            lead,
            capActionAuditTrail: AuditTrailKind.CapBPSearchFailed,
            success: false,
            errorMessage: businessPartnerDetailData.error,
        });
        throw new Error(businessPartnerDetailData.error);
    }

    if (!businessPartnerDetailData?.d?.results?.length) {
        return { count: 0, items: [] };
    }

    const {
        d: { results, __count: count },
    } = businessPartnerDetailData;

    const mappedBusinessPartner = await Promise.all(
        results.map(async result => ({
            businessPartnerGuid: result.BusinessPartnerGuid,
            businessPartnerId: result.BusinessPartnerId,
            fullName: result.NameFirst || result.NameLast ? [result.NameFirst, result.NameLast].join(' ') : '',
            firstName: result.NameFirst ?? '',
            lastName: result.NameLast,
            lastNameFront: result.NameLast,
            firstNameJapan: result.NameMiddle,
            lastNameJapan: result.NameLastOther,
            dateOfBirth: !isEmpty(result.DateOfBirth) ? dateFromCapDateFormat(result.DateOfBirth).toDate() : undefined,
            email: result.EMailAddress,
            phone: {
                prefix: result.BpData_To_BpComm.results.length
                    ? getPhonePrefix(result.BpData_To_BpComm.results[0].Country)
                    : undefined,
                value: result.MobileNumber ?? '',
            },
            telephone: {
                prefix: result.BpData_To_BpComm.results.length
                    ? getPhonePrefix(result.BpData_To_BpComm.results[0].Country)
                    : undefined,
                value: result.TelephoneNumber ?? '',
            },
            title: result.Title ? getTitleByCapCode(result.Title) : undefined,
            maritalStatus: result.BpData_To_BpDemographics?.results?.length
                ? getMaritalByCapCode(result.BpData_To_BpDemographics.results[0].MaritalStatus)
                : undefined,
            gender: result.BpData_To_BpDemographics?.results?.length
                ? getGenderByCapCode(result.BpData_To_BpDemographics.results[0].Sex)
                : undefined,
            companyName: result.CompanyName1,
            businessTitle: result.BusinessTitle,
            companyPhoeticName: getCompanyPhoeticName(result.CompanyName2),
            occupation: getOccupation(result.CompanyName2),
            ...(result.BpData_To_BpAddress?.results?.length
                ? getCustomerAdressFromCap(result.BpData_To_BpAddress.results[0])
                : {}),
            ...(await getResponsibleSalesPersonAndDealerFromCap(result, loaders)),
            isActive: !result.Xdele && result.DealerArchivingFlag !== 'X',
        }))
    );

    const businessPartnerIds = mappedBusinessPartner.map(bp => bp.businessPartnerId);
    */

    const customerCompetitorVehicleResultData = await getCustomerCompetitorVehicleDetails({
        ...capSetting,
        auth: authentication.access_token,
        customerIds: businessPartnerIds,
    });

    // This is the start of old function
    const competitorVehicleFromCustomerData =
        !customerCompetitorVehicleResultData?.error && customerCompetitorVehicleResultData?.d?.results?.length
            ? customerCompetitorVehicleResultData.d.results
            : [];

    const competitorVehicleFromBpData = uniqBy(
        'businessPartnerId',
        flatten(
            businessPartnerDetailData
                .map(bpDetail => {
                    if (bpDetail.error || !bpDetail.d?.results.length) {
                        return null;
                    }

                    const {
                        d: { results },
                    } = bpDetail;

                    return results
                        .map((result: BusinessPartnerResData) => {
                            if (!businessPartnerIds.includes(result.BusinessPartnerId)) {
                                return null;
                            }

                            return {
                                businessPartnerId: result.BusinessPartnerId,
                                competitorVehicle: result.BpData_To_BpCompetitorVehicle?.results?.length
                                    ? result.BpData_To_BpCompetitorVehicle.results[0]
                                    : null,
                            };
                        })
                        .filter(Boolean);
                })
                .filter(Boolean)
        )
    );
    // End of old function

    // DO NOT REMOVE THIS
    /*
    const competitorVehicleFromCustomerData =
        !customerCompetitorVehicleResultData?.error && customerCompetitorVehicleResultData?.d?.results?.length
            ? customerCompetitorVehicleResultData.d.results
            : [];

    const competitorVehicleFromBpData = results.map((result: BusinessPartnerResData) => {
        if (!businessPartnerIds.includes(result.BusinessPartnerId)) {
            return null;
        }

        return {
            businessPartnerId: result.BusinessPartnerId,
            competitorVehicle: result.BpData_To_BpCompetitorVehicle?.results?.length
                ? result.BpData_To_BpCompetitorVehicle.results[0]
                : null,
        };
    });
    */

    const combinedCompetitorVehicle = await Promise.all(
        businessPartnerIds.map(async businessPartnerId => {
            const [vehicleFromCustomer, vehicleFromBp] = [
                competitorVehicleFromCustomerData.find(vehicle => vehicle.customerId === businessPartnerId),
                competitorVehicleFromBpData.find(vehicle => vehicle.businessPartnerId === businessPartnerId),
            ];

            const capCustomerCurrentVehicle = formatBpCompetitorVehicleReturnValue(
                vehicleFromBp?.competitorVehicle,
                vehicleFromCustomer
            );

            const currentVehicle =
                competitorVehicleFromCustomerData?.length || competitorVehicleFromBpData
                    ? await getCustomerCurrentVehicleFromCap(capCustomerCurrentVehicle)
                    : undefined;

            return vehicleFromCustomer || vehicleFromBp?.competitorVehicle
                ? { businessPartnerId, currentVehicle }
                : { businessPartnerId, currentVehicle: null };
        })
    );

    const uniqueBusinessPartnersWithHobby = await Promise.all(
        uniqueBusinessPartners.map(async bpData => {
            const { existingHobbiesMap } = await getCustomerExistingHobbies({
                customerGuid: bpData.businessPartnerGuid,
                companyCode: company?.countryCode,
                t,
                lead,
                ...capSetting,
                auth: authentication.access_token,
            });

            const hobbyKeys = Array.from(existingHobbiesMap.values())
                ?.filter(hobby => hobby.isValid)
                .map(hobby => hobby.hobbyKey);

            const currentVehicle = combinedCompetitorVehicle.find(
                competitor => competitor.businessPartnerId === bpData.businessPartnerId
            );

            return {
                ...bpData,
                hobby: hobbyKeys,
                ...(currentVehicle?.currentVehicle ? { currentVehicle: currentVehicle.currentVehicle } : {}),
            };
        })
    );

    // Start of Adjusted return to following old query result
    return {
        count: uniqueBusinessPartnersWithHobby.length,
        items: uniqueBusinessPartnersWithHobby,
    };
    // End of Adjusted return to following old query result

    // DO NOT REMOVE THIS
    /*
    return {
        count: parseInt(count, 10),
        // Inactive BP will be placed on latest rows
        items: mappedBusinessPartner
            .sort((a, b) => Number(b.isActive) - Number(a.isActive))
            .map(bpData => {
                const currentVehicle = combinedCompetitorVehicle.find(
                    competitor => competitor.businessPartnerId === bpData.businessPartnerId
                );

            return {
                ...bpData,
                ...(currentVehicle?.currentVehicle ? { currentVehicle: currentVehicle.currentVehicle } : {}),
            };
        }),
    }; */
};

export default requiresLoggedUser(query);
