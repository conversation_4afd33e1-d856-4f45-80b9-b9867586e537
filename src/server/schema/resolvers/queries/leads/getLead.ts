import { LeadStatus } from '../../../../database/documents/Lead';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { LeadPolicyAction } from '../../../../permissions/types/leads';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLQueryResolvers } from '../../definitions';

const query: GraphQLQueryResolvers['getLead'] = async (root, { id }, { getPermissionController }) => {
    const { collections } = await getDatabaseContext();
    const permissions = await getPermissionController();

    const permissionFilter = await permissions.leads.getFilterQueryForActions([
        LeadPolicyAction.View,
        LeadPolicyAction.ViewContact,
    ]);

    const lead = await collections.leads.findOne({
        $and: [
            {
                '_versioning.suiteId': id,
                '_versioning.isLatest': true,
                isDraft: false,
                isDeleted: false,
            },
            permissionFilter,
        ],
    });

    if (lead) {
        return lead;
    }

    // try to return merged lead if it exists (with identifier not empty)
    return collections.leads.findOne(
        {
            $and: [
                {
                    '_versioning.suiteId': id,
                    status: { $in: [LeadStatus.Merged, LeadStatus.Merging] },
                    isLead: false,
                    isDeleted: false,
                },
                { $and: [{ identifier: { $ne: null } }, { identifier: { $ne: '' } }] },
                permissionFilter,
            ],
        },
        { sort: { '_versioning.updatedAt': -1 } }
    );
};

export default requiresLoggedUser(query);
