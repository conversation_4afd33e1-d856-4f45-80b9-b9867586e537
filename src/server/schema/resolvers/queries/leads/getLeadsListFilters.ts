import { ObjectId } from 'mongodb';
import { LeadStatus, ModuleType, applicationModuleTypes } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { requiresLoggedUser } from '../../../middlewares';
import type { GraphQLQueryResolvers } from '../../definitions';

const query: GraphQLQueryResolvers['getLeadsListFilters'] = async (root, { filter }, { getPermissionController }) => {
    const { collections } = await getDatabaseContext();

    const companies = await collections.companies
        .find({
            ...(filter.companyIds?.length > 0 && {
                _id: { $in: filter.companyIds.map(companyId => new ObjectId(companyId)) },
            }),
        })
        .toArray();

    // include all application module types and Porsche Retain Module
    const moduleTypes = [...applicationModuleTypes, ModuleType.PorscheRetainModule];

    const modules = await collections.modules
        .find({
            companyId: { $in: companies.map(company => company._id) },
            _type: { $in: moduleTypes },
        })
        .toArray();

    const filterStatuses = [LeadStatus.Lost, LeadStatus.Completed, LeadStatus.Merging];
    if (!filter.includeMergedStatus) {
        filterStatuses.push(LeadStatus.Merged);
    }

    return {
        leadStatus: Object.values(LeadStatus).filter(status => !filterStatuses.includes(status)),
        modules,
        companies,
    };
};

export default requiresLoggedUser(query);
