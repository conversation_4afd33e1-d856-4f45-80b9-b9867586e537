import { isEmpty } from 'lodash/fp';
import { ppnAuth, searchBusinessPartner } from '../../../../integrations/cap';
import type { BusinessPartnerResData } from '../../../../integrations/cap/businessPartner/types';
import { getCustomerCompetitorVehicleDetails } from '../../../../integrations/cap/customerCompetitorVehicle';
import { getCustomerCurrentVehicleFromCap } from '../../../../integrations/cap/utils';
// eslint-disable-next-line max-len
import formatBpCompetitorVehicleReturnValue from '../../../../integrations/cap/utils/formatBpCompetitorVehicleReturnValue';
import formatBusinessPartnerReturnValue from '../../../../integrations/cap/utils/formatBusinessPartnerReturnValue';
import type { GenericGetResult } from '../../../../integrations/cap/utils/types';
import { ModulePolicyAction } from '../../../../permissions/types/modules';
import { emailRegex } from '../../../../utils/validators';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import type { GraphQLQueryResolvers } from '../../definitions';

const query: GraphQLQueryResolvers['getContactsFromBp'] = async (
    root,
    { pagination, applicationModuleId, capModuleId, dealerId, query },
    { loaders, getTranslations, getPermissionController }
) => {
    const { modules: modulePermission } = await getPermissionController();
    const { t } = await getTranslations(['auditTrails']);

    const applicationModule = await loaders.moduleById.load(applicationModuleId);

    if (!applicationModule) {
        throw new InvalidInput({ module: `Module of this application is invalid.` });
    }

    const company = await loaders.companyById.load(applicationModule.companyId);
    if (!company) {
        throw new InvalidInput({ module: `Company of this application is invalid.` });
    }

    // User access check
    if (!modulePermission.mayOperateOn(applicationModule, ModulePolicyAction.CreateContact)) {
        throw new InvalidPermission();
    }

    const capSetting = await loaders.capSettingByModuleId.load(capModuleId);

    const authentication = await ppnAuth(capSetting);
    if (authentication.error) {
        const errorMessage = t('auditTrails:application.defaultCapError.authFailed');
        throw new Error(errorMessage);
    }

    const getCustomerData = async () => {
        const dealerData = await loaders.dealerById.load(dealerId);
        const numberRegex = /^\d+$/;

        return {
            email: emailRegex.test(query) ? query : null,
            mobileNumber: numberRegex.test(query) ? query : null,
            dealer: dealerData.integrationDetails?.dealerCode ?? '',
        };
    };

    const customerData = await getCustomerData();

    if (isEmpty(customerData.dealer)) {
        return { count: 0, items: [] };
    }

    const businessPartnerDetailData: GenericGetResult<BusinessPartnerResData> = await searchBusinessPartner({
        ...capSetting,
        t,
        auth: authentication.access_token,
        query: {
            page: Math.floor(pagination.offset / pagination.limit) + 1,
            size: pagination.limit,
            email: customerData.email,
            mobileNumber: customerData.mobileNumber,
            dealer: customerData.dealer,
        },
    });

    if (businessPartnerDetailData?.error) {
        throw new Error(businessPartnerDetailData.error);
    }

    if (!businessPartnerDetailData?.d?.results?.length) {
        return { count: 0, items: [] };
    }

    const {
        d: { results, __count: count },
    } = businessPartnerDetailData;

    const mappedBusinessPartner = await Promise.all(
        results.map(async result => formatBusinessPartnerReturnValue(result, company, loaders))
    );

    const businessPartnerIds = mappedBusinessPartner.map(bp => bp.businessPartnerId);

    const customerCompetitorVehicleResultData = await getCustomerCompetitorVehicleDetails({
        ...capSetting,
        auth: authentication.access_token,
        customerIds: businessPartnerIds,
    });

    const competitorVehicleFromCustomerData =
        !customerCompetitorVehicleResultData?.error && customerCompetitorVehicleResultData?.d?.results?.length
            ? customerCompetitorVehicleResultData.d.results
            : [];

    const competitorVehicleFromBpData = results.map((result: BusinessPartnerResData) => {
        if (!businessPartnerIds.includes(result.BusinessPartnerId)) {
            return null;
        }

        return {
            businessPartnerId: result.BusinessPartnerId,
            competitorVehicle: result.BpData_To_BpCompetitorVehicle?.results?.length
                ? result.BpData_To_BpCompetitorVehicle.results[0]
                : null,
        };
    });

    const combinedCompetitorVehicle = await Promise.all(
        businessPartnerIds.map(async businessPartnerId => {
            const vehicleFromCustomer = competitorVehicleFromCustomerData.find(
                vehicle => vehicle.customerId === businessPartnerId
            );
            const vehicleFromBp = competitorVehicleFromBpData.find(
                vehicle => vehicle.businessPartnerId === businessPartnerId
            );

            const capCustomerCurrentVehicle = formatBpCompetitorVehicleReturnValue(
                vehicleFromBp?.competitorVehicle,
                vehicleFromCustomer
            );

            const currentVehicle =
                competitorVehicleFromCustomerData?.length || competitorVehicleFromBpData
                    ? await getCustomerCurrentVehicleFromCap(capCustomerCurrentVehicle)
                    : undefined;

            return vehicleFromCustomer || vehicleFromBp?.competitorVehicle
                ? { businessPartnerId, currentVehicle }
                : { businessPartnerId, currentVehicle: null };
        })
    );

    return {
        count: parseInt(count, 10),
        // Inactive BP will be placed on latest rows
        items: mappedBusinessPartner
            .sort((a, b) => Number(b.isActive) - Number(a.isActive))
            .map(bpData => {
                const currentVehicle = combinedCompetitorVehicle.find(
                    competitor => competitor.businessPartnerId === bpData.businessPartnerId
                );

                return {
                    ...bpData,
                    ...(currentVehicle?.currentVehicle ? { currentVehicle: currentVehicle.currentVehicle } : {}),
                };
            }),
    };
};

export default requiresLoggedUser(query);
