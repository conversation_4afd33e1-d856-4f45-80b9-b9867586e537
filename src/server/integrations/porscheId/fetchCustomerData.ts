import { URLSearchParams } from 'url';
import * as Sentry from '@sentry/node';
import fetch from 'node-fetch';
import type { PorscheIdSetting } from '../../database/documents/Setting';
import type { PorscheIdData } from './types';
import { getPhonePrefixByCountryCode, titleSalutationMap } from './utils/processCustomerDataToKYC';

type FetchCustomerDataFn = (
    porscheIdSetting: PorscheIdSetting,
    countryCode: string,
    auth: string
) => Promise<PorscheIdData>;

const fetchCustomerData: FetchCustomerDataFn = async (porscheIdSetting, countryCode, auth) => {
    const {
        secrets: { apiKey, userDataBaseUrl },
    } = porscheIdSetting;

    const paramsDetails = {
        'Accept-Language': 'en_US',
        country: countryCode,
    };

    const params = new URLSearchParams();
    const paramsKeys = Object.keys(paramsDetails);
    paramsKeys.forEach(key => {
        params.append(key, paramsDetails[key]);
    });

    const profileDataUrl = `${userDataBaseUrl}/user/v1?${params.toString()}`;

    const requestHeaders = {
        accept: 'application/json',
        'Accept-Language': 'en_US',
        apiKey,
        Authorization: `Bearer ${auth}`,
    };

    try {
        const response = await fetch(profileDataUrl, {
            headers: requestHeaders,
            method: 'GET',
        });

        if (response.status !== 200) {
            const responseText = await response.text();

            Sentry.withScope(scope => {
                scope.clearBreadcrumbs();
                scope.setTag('api', 'Porsche ID');
                scope.setContext('payload', {
                    endpoint: 'GET Fetch Customer Data',
                    data: {
                        endpoint: profileDataUrl,
                        headers: requestHeaders,
                    },
                });
                Sentry.captureException(
                    JSON.stringify({ status: response?.status, responseData: responseText }, null, 4)
                );
            });

            console.error(`Error found in getting Porsche ID customer data: ${response.status}`);
            console.error(responseText);

            return null;
        }

        const responseData = await response.json();

        const userTitle = titleSalutationMap[responseData.salutation];
        const userAddress = responseData.addresses?.[0];
        const userEmail = responseData.emails?.[0];
        const userMobile = responseData.mobiles?.[0];
        const userPhone = responseData.phones?.[0];
        const userVehicle = responseData.vehicles?.[0];

        const countryPhonePrefix = userAddress ? getPhonePrefixByCountryCode(userAddress.country) : null;
        const mobileValue = userMobile?.number ? userMobile.number.replace(`+${countryPhonePrefix}`, '') : null;
        const phoneValue = userPhone?.number ? userPhone.number.replace(`+${countryPhonePrefix}`, '') : null;

        const porscheIdData = {
            porscheId: responseData.porscheId,
            porscheCiamId: responseData.ciamId,
            title: userTitle,
            firstName: responseData.firstName,
            lastName: responseData.lastName,
            email: userEmail?.email,
            dateOfBirth: responseData.dateOfBirth,
            mobile: { prefix: countryPhonePrefix ? parseInt(countryPhonePrefix, 10) : null, value: mobileValue },
            phone: { prefix: countryPhonePrefix ? parseInt(countryPhonePrefix, 10) : null, value: phoneValue },
            address: userAddress,
            vin: userVehicle?.vin,
        };

        return porscheIdData;
    } catch (error) {
        Sentry.withScope(scope => {
            scope.clearBreadcrumbs();
            scope.setTag('api', 'Porsche ID');
            scope.setContext('payload', {
                endpoint: 'POST Fetch Customer Data',
                data: {
                    endpoint: profileDataUrl,
                    headers: requestHeaders,
                },
            });
            Sentry.captureException(error);
        });

        console.error(`Error found in fetch customer data: ${error}`);

        return null;
    }
};

export default fetchCustomerData;
