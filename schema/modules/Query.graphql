extend type Query {
    """
    List modules
    """
    listModules(pagination: Pagination, filter: ModuleFilteringRule, sort: ModuleSortingRule): PaginatedModules!

    """
    List Module Type
    """
    listModuleType(filter: ModuleFilteringRule): [ModuleType!]!

    """
    Get a module by its ID
    """
    getModule(id: ObjectID!): Module

    """
    List MyInfo Modules
    """
    listMyInfoModules(pagination: Pagination, filter: ModuleFilteringRule): PaginatedMyInfoModules!

    """
    Retreive booked appointment slots
    """
    retrieveBlockedAppointmentTimeSlot(moduleId: ObjectID, eventId: ObjectID): [DateTime!]!

    """
    List WebCalc Settings
    """
    listWebCalcSettings(moduleId: ObjectID!, pagination: Pagination): PaginatedWebCalcSettings!

    """
    List Modules for Application Download
    """
    listModulesForApplicationDownload(filter: ModuleFilteringRule): [Module!]!

    """
    List Mobility Locations
    """
    listLocations(
        pagination: Pagination
        filter: LocationFilteringRule
        sort: LocationSortingRule
    ): PaginatedMobilityLocation!

    """
    Get Mobility Location
    """
    getMobilityLocation(locationId: ObjectID!): MobilityLocation

    """
    Get Home Delivery
    """
    getMobilityHomeDelivery(deliveryId: ObjectID!): MobilityModule

    """
    list Home Deliveries
    """
    listMobilityHomeDeliveries(
        pagination: Pagination
        filter: HomeDeliveryFilteringRule
        sort: HomeDeliverySortingRule
    ): PaginatedMobilityHomeDelivery!

    listLocationConditions(locationId: ObjectID!): [String!]!

    """
    Get OFR RV table values from imported excel
    """
    getImportedOFRRVTableFromExcel(upload: Upload!): OfrImportedRVTable!
}
