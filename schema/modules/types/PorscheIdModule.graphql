type PorscheIdModule implements Module {
    """
    UID
    """
    id: ObjectID!

    """
    Company ID
    """
    companyId: ObjectID!

    """
    Company
    """
    company: Company!

    """
    Display name
    """
    displayName: String!

    """
    Porsche ID Integration Setting
    """
    porscheIdSetting: PorscheIdSetting!

    """
    Permissions
    """
    permissions: [String]!

    """
    Versioning
    """
    versioning: SimpleVersioning!
}

type PorscheIdSetting {
    """
    For Porsche ID Authorization
    """
    apiKey: String!
    identityProvider: String!
    audience: String!
    """
    User Data Base URL for fetching customer data
    """
    userDataBaseUrl: String!
}
