extend type Query {
    """
    Get an application by its ID
    """
    getApplication(id: ObjectID!): Application

    """
    Get an application signing status
    """
    getApplicationSigningStatus(token: String!): ApplicationSigningStatus

    """
    Get an application journey
    """
    getApplicationJourney(token: String!, refreshToken: Boolean): ApplicationJourney!

    """
    Get a sales offer journey
    """
    getSalesOfferJourney(token: String!): SalesOfferJourney!

    """
    List applications
    """
    listApplications(
        pagination: Pagination
        sort: ApplicationSortingRule
        filter: ApplicationFilteringRule
    ): PaginatedApplications!

    """
    List Application Status
    """
    listApplicationsStatus(filter: ApplicationFilteringRule): [ApplicationStatus!]!

    """
    List Application Module
    """
    listApplicationsModules(filter: ApplicationFilteringRule): [Module!]!

    """
    Get Application List Filters
    """
    getApplicationsListFilters(filter: ApplicationFilteringRule): ApplicationFilter!

    """
    Get Mobility Application List Filters
    """
    getMobilityApplicationsListFilters(filter: ApplicationFilteringRule): MobilityApplicationFilter!

    """
    Get download link for an application document
    """
    getApplicationDocumentDownloadLink(
        applicationId: ObjectID!
        documentId: ObjectID!
        stage: ApplicationStage
    ): Download

    """
    Get audit trails for an application
    """
    getApplicationAuditTrails(
        applicationId: ObjectID!
        stage: ApplicationStage
        pagination: Pagination
    ): PaginatedAuditTrails!

    """
    Get audit trail kinds for an application
    """
    getApplicationAuditTrailKinds(applicationId: ObjectID!, stage: ApplicationStage): [AuditTrailKind!]!
}
