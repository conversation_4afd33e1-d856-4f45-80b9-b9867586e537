{"title": "Module - {{name}}", "descriptions": {"displayName": "Name", "company": "Company"}, "actions": {"label": "Actions", "update": "Update", "cancel": "Cancel"}, "messages": {"updateSubmitting": "Updating details..", "updateSuccessful": "Main Details updated", "updateEmailSection": "Updating Email Section...", "updateEmailSectionSuccessful": "Email Section updated"}, "mainActionButtons": {"delete": "Delete"}, "fields": {"emails": {"subject": {"label": "Subject"}, "introTitle": {"label": "Intro Title"}, "contentText": {"label": "Content Text"}, "isShowSummaryVehicle": {"label": "Summary Vehicle Visible"}, "introImage": {"label": "Intro Image"}}}, "emailContents": {"title": "Email Contents", "common": {"subject": {"label": "Subject"}, "introTitle": {"label": "Intro Title"}, "contentText": {"label": "Content Text"}, "introImage": {"label": "Intro Image"}, "isShowSummaryVehicle": {"label": "Show Summary Vehicle"}}, "salesConsultant": {"equityNotification": {"label": "Equity Notification"}}, "customer": {"newOffer": {"label": "<PERSON> Offer"}}}, "tabs": {"moduleDetails": "Main Details", "emailContents": "Email Contents"}, "rvTable": {"title": "RV Table", "importModalTitle": "Import RV Table", "columns": {"model": "Model / Model Year", "year": "Year"}, "actions": {"importExcel": "Import Excel", "exportExcel": "Export Excel", "close": "Close", "uploadFile": "Upload File"}, "errors": {"missingModelAtRows": "Missing Model at rows: {{rowNumbers}}", "missingYearAtColumns": "Missing Year at columns: {{colNumbers}}", "numericFieldsMustBeGreaterThanOrEqualToZeroAtRow": "Numeric Fields must be greater than or equal to zero at row #{{rowNumber}}: {{colNumbers}}", "duplicateModelsFound": "Duplicate Model(s) found: {{models}}", "duplicateYearsFound": "Duplicate Year(s) found: {{years}}"}}}